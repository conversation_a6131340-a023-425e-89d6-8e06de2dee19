{"id": "7525849844863323414", "formats": [{"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "download", "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/o0kBcRTArTDDeFuzgXERVyfAEhFFiYEogQgIYA/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2840&bt=1420&cs=0&ds=3&ft=4KJMyMzm8Zmo0xsmPI4jVyPuQpWrKsd.&mime_type=video_mp4&qs=0&rc=NWU6M2c1OGg6M2QzPGk2Z0BpM2o5ZHE5cmZtNDMzbzczNUAwYi1jY2FeNS4xM18vMF9eYSNsbGAuMmRrLWxhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796680&l=202507152356540AAE74DCBB209424A8AB&ply_type=2&policy=2&signature=ec2f9ed4e7931bce4400a83a5e118c41&tk=tt_chain_token", "format_note": "watermarked", "preference": -2, "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "dynamic_range": "SDR", "cookies": "ttwid=1%7C1D19G2Rnu8sEUem9pjD3PClUB-peOojCnRfk_zHQ_vk%7C1752623813%7C897427a0ac4a92ab03eecc1123cd403ffd08e0f518bd0d99e75567f5654517e9; Domain=.tiktok.com; Path=/; Expires=1783727813; tt_csrf_token=WXociDln-IVgUrkDUJCM2tG9uvwr2adDzHHM; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"2LidcReqjGrmVfZFjMiYiQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175814", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525849844863323414"}, "format": "download - unknown (watermarked)"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_1436241-0", "tbr": 1436, "quality": 1, "preference": -1, "filesize": 11944864, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/o4BxpEQiZBLZsQaIALwChEwwvMiBE3AMBKHDY/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2804&bt=1402&cs=0&ds=6&ft=4KJMyMzm8Zmo0xsmPI4jVyPuQpWrKsd.&mime_type=video_mp4&qs=0&rc=OzppN2k2NWlkNjYzZTg5OEBpM2o5ZHE5cmZtNDMzbzczNUAtMjFhYy1eXmMxXjItYjM2YSNsbGAuMmRrLWxhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796680&l=202507152356540AAE74DCBB209424A8AB&ply_type=2&policy=2&signature=854147ad77cb97b431c2bbb675845a18&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C1D19G2Rnu8sEUem9pjD3PClUB-peOojCnRfk_zHQ_vk%7C1752623813%7C897427a0ac4a92ab03eecc1123cd403ffd08e0f518bd0d99e75567f5654517e9; Domain=.tiktok.com; Path=/; Expires=1783727813; tt_csrf_token=WXociDln-IVgUrkDUJCM2tG9uvwr2adDzHHM; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"2LidcReqjGrmVfZFjMiYiQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175814", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525849844863323414"}, "format": "h264_540p_1436241-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_1436241-1", "tbr": 1436, "quality": 1, "preference": -1, "filesize": 11944864, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/o4BxpEQiZBLZsQaIALwChEwwvMiBE3AMBKHDY/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2804&bt=1402&cs=0&ds=6&ft=4KJMyMzm8Zmo0xsmPI4jVyPuQpWrKsd.&mime_type=video_mp4&qs=0&rc=OzppN2k2NWlkNjYzZTg5OEBpM2o5ZHE5cmZtNDMzbzczNUAtMjFhYy1eXmMxXjItYjM2YSNsbGAuMmRrLWxhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796680&l=202507152356540AAE74DCBB209424A8AB&ply_type=2&policy=2&signature=854147ad77cb97b431c2bbb675845a18&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C1D19G2Rnu8sEUem9pjD3PClUB-peOojCnRfk_zHQ_vk%7C1752623813%7C897427a0ac4a92ab03eecc1123cd403ffd08e0f518bd0d99e75567f5654517e9; Domain=.tiktok.com; Path=/; Expires=1783727813; tt_csrf_token=WXociDln-IVgUrkDUJCM2tG9uvwr2adDzHHM; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"2LidcReqjGrmVfZFjMiYiQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175814", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525849844863323414"}, "format": "h264_540p_1436241-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_595297-0", "tbr": 595, "quality": 1, "preference": -1, "filesize": 4950941, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/okChYJZiavqEuBpsEMwmNAEQx3HQIBEDBMiwZ/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1162&bt=581&cs=2&ds=6&ft=4KJMyMzm8Zmo0xsmPI4jVyPuQpWrKsd.&mime_type=video_mp4&qs=11&rc=OThkaTlnNjRpMzhmMzpkOUBpM2o5ZHE5cmZtNDMzbzczNUBhMTEwYWJiNi0xLy00NmI2YSNsbGAuMmRrLWxhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796680&l=202507152356540AAE74DCBB209424A8AB&ply_type=2&policy=2&signature=6fb72d0fde571e9e60dec07c40e78ed9&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C1D19G2Rnu8sEUem9pjD3PClUB-peOojCnRfk_zHQ_vk%7C1752623813%7C897427a0ac4a92ab03eecc1123cd403ffd08e0f518bd0d99e75567f5654517e9; Domain=.tiktok.com; Path=/; Expires=1783727813; tt_csrf_token=WXociDln-IVgUrkDUJCM2tG9uvwr2adDzHHM; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"2LidcReqjGrmVfZFjMiYiQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175814", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525849844863323414"}, "format": "bytevc1_540p_595297-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_595297-1", "tbr": 595, "quality": 1, "preference": -1, "filesize": 4950941, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/okChYJZiavqEuBpsEMwmNAEQx3HQIBEDBMiwZ/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1162&bt=581&cs=2&ds=6&ft=4KJMyMzm8Zmo0xsmPI4jVyPuQpWrKsd.&mime_type=video_mp4&qs=11&rc=OThkaTlnNjRpMzhmMzpkOUBpM2o5ZHE5cmZtNDMzbzczNUBhMTEwYWJiNi0xLy00NmI2YSNsbGAuMmRrLWxhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796680&l=202507152356540AAE74DCBB209424A8AB&ply_type=2&policy=2&signature=6fb72d0fde571e9e60dec07c40e78ed9&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C1D19G2Rnu8sEUem9pjD3PClUB-peOojCnRfk_zHQ_vk%7C1752623813%7C897427a0ac4a92ab03eecc1123cd403ffd08e0f518bd0d99e75567f5654517e9; Domain=.tiktok.com; Path=/; Expires=1783727813; tt_csrf_token=WXociDln-IVgUrkDUJCM2tG9uvwr2adDzHHM; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"2LidcReqjGrmVfZFjMiYiQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175814", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525849844863323414"}, "format": "bytevc1_540p_595297-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_730233-0", "tbr": 730, "quality": 2, "preference": -1, "filesize": 6073167, "width": 720, "height": 1280, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oYEApEY3MpBwEyCZYFQhBIawMoxsmBhisQiHv/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1426&bt=713&cs=2&ds=3&ft=4KJMyMzm8Zmo0xsmPI4jVyPuQpWrKsd.&mime_type=video_mp4&qs=14&rc=ZDozaGk1NjhkMztlZGRmN0BpM2o5ZHE5cmZtNDMzbzczNUA1Ly40MWFgX14xMzMzYC1fYSNsbGAuMmRrLWxhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796680&l=202507152356540AAE74DCBB209424A8AB&ply_type=2&policy=2&signature=95dc741f95aec80fa084abff3293e9ec&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C1D19G2Rnu8sEUem9pjD3PClUB-peOojCnRfk_zHQ_vk%7C1752623813%7C897427a0ac4a92ab03eecc1123cd403ffd08e0f518bd0d99e75567f5654517e9; Domain=.tiktok.com; Path=/; Expires=1783727813; tt_csrf_token=WXociDln-IVgUrkDUJCM2tG9uvwr2adDzHHM; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"2LidcReqjGrmVfZFjMiYiQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175814", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525849844863323414"}, "format": "bytevc1_720p_730233-0 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_730233-1", "tbr": 730, "quality": 2, "preference": -1, "filesize": 6073167, "width": 720, "height": 1280, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oYEApEY3MpBwEyCZYFQhBIawMoxsmBhisQiHv/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1426&bt=713&cs=2&ds=3&ft=4KJMyMzm8Zmo0xsmPI4jVyPuQpWrKsd.&mime_type=video_mp4&qs=14&rc=ZDozaGk1NjhkMztlZGRmN0BpM2o5ZHE5cmZtNDMzbzczNUA1Ly40MWFgX14xMzMzYC1fYSNsbGAuMmRrLWxhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796680&l=202507152356540AAE74DCBB209424A8AB&ply_type=2&policy=2&signature=95dc741f95aec80fa084abff3293e9ec&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C1D19G2Rnu8sEUem9pjD3PClUB-peOojCnRfk_zHQ_vk%7C1752623813%7C897427a0ac4a92ab03eecc1123cd403ffd08e0f518bd0d99e75567f5654517e9; Domain=.tiktok.com; Path=/; Expires=1783727813; tt_csrf_token=WXociDln-IVgUrkDUJCM2tG9uvwr2adDzHHM; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"2LidcReqjGrmVfZFjMiYiQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175814", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525849844863323414"}, "format": "bytevc1_720p_730233-1 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_1165473-0", "tbr": 1165, "quality": 3, "preference": -1, "filesize": 9691492, "width": 1080, "height": 1920, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/ooJxhP3sDZvEMHdiEpnwCQYMBQEaBihwOBUAI/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2276&bt=1138&cs=2&ds=4&ft=4KJMyMzm8Zmo0xsmPI4jVyPuQpWrKsd.&mime_type=video_mp4&qs=15&rc=ZTU6ZGg1PDM8OmRnaDg0ZEBpM2o5ZHE5cmZtNDMzbzczNUBjNmMyNjMwXzQxYjZjY2JfYSNsbGAuMmRrLWxhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796680&l=202507152356540AAE74DCBB209424A8AB&ply_type=2&policy=2&signature=4f7311ab7f30eb74b969cde4302e09d1&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C1D19G2Rnu8sEUem9pjD3PClUB-peOojCnRfk_zHQ_vk%7C1752623813%7C897427a0ac4a92ab03eecc1123cd403ffd08e0f518bd0d99e75567f5654517e9; Domain=.tiktok.com; Path=/; Expires=1783727813; tt_csrf_token=WXociDln-IVgUrkDUJCM2tG9uvwr2adDzHHM; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"2LidcReqjGrmVfZFjMiYiQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175814", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525849844863323414"}, "format": "bytevc1_1080p_1165473-0 - 1080x1920"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_1165473-1", "tbr": 1165, "quality": 3, "preference": -1, "filesize": 9691492, "width": 1080, "height": 1920, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/ooJxhP3sDZvEMHdiEpnwCQYMBQEaBihwOBUAI/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2276&bt=1138&cs=2&ds=4&ft=4KJMyMzm8Zmo0xsmPI4jVyPuQpWrKsd.&mime_type=video_mp4&qs=15&rc=ZTU6ZGg1PDM8OmRnaDg0ZEBpM2o5ZHE5cmZtNDMzbzczNUBjNmMyNjMwXzQxYjZjY2JfYSNsbGAuMmRrLWxhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796680&l=202507152356540AAE74DCBB209424A8AB&ply_type=2&policy=2&signature=4f7311ab7f30eb74b969cde4302e09d1&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C1D19G2Rnu8sEUem9pjD3PClUB-peOojCnRfk_zHQ_vk%7C1752623813%7C897427a0ac4a92ab03eecc1123cd403ffd08e0f518bd0d99e75567f5654517e9; Domain=.tiktok.com; Path=/; Expires=1783727813; tt_csrf_token=WXociDln-IVgUrkDUJCM2tG9uvwr2adDzHHM; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"2LidcReqjGrmVfZFjMiYiQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175814", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525849844863323414"}, "format": "bytevc1_1080p_1165473-1 - 1080x1920"}], "subtitles": {}, "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525849844863323414"}, "channel": "<PERSON>", "channel_id": "MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader": "dylan.page", "uploader_id": "6760368158794155013", "channel_url": "https://www.tiktok.com/@MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader_url": "https://www.tiktok.com/@dylan.page", "track": "original sound", "artists": ["<PERSON>"], "duration": 66, "title": "1M likes and I’m growing the empire… one robot at a time 🦾🏰", "description": "1M likes and I’m growing the empire… one robot at a time 🦾🏰", "timestamp": 1752248469, "view_count": 4800000, "like_count": 681400, "repost_count": 46700, "comment_count": 6134, "thumbnails": [{"id": "dynamicCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/os7vMQABeAFRA2GpIJnxEfIAeLjSm4QAyIIELy~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=832NJ%2Fu%2BIPW%2F%2BaplI2qRF7%2FZIrM%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -2}, {"id": "cover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/os7vMQABeAFRA2GpIJnxEfIAeLjSm4QAyIIELy~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=832NJ%2Fu%2BIPW%2F%2BaplI2qRF7%2FZIrM%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}, {"id": "originCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/o8wgThYyAFAETukoRegBFYDnSEQxQCFitTwcfD~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=eCkmjv74yFw%2FyiDjMO5lImtwGPE%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}], "webpage_url": "https://www.tiktok.com/@dylan.page/video/7525849844863323414", "webpage_url_basename": "7525849844863323414", "webpage_url_domain": "tiktok.com", "extractor": "TikTok", "extractor_key": "TikTok", "thumbnail": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/o8wgThYyAFAETukoRegBFYDnSEQxQCFitTwcfD~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=eCkmjv74yFw%2FyiDjMO5lImtwGPE%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "display_id": "7525849844863323414", "fulltitle": "1M likes and I’m growing the empire… one robot at a time 🦾🏰", "duration_string": "1:06", "upload_date": "20250711", "artist": "<PERSON>", "epoch": 1752623813, "ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_595297-1", "tbr": 595, "quality": 1, "preference": -1, "filesize": 4950941, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/okChYJZiavqEuBpsEMwmNAEQx3HQIBEDBMiwZ/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1162&bt=581&cs=2&ds=6&ft=4KJMyMzm8Zmo0xsmPI4jVyPuQpWrKsd.&mime_type=video_mp4&qs=11&rc=OThkaTlnNjRpMzhmMzpkOUBpM2o5ZHE5cmZtNDMzbzczNUBhMTEwYWJiNi0xLy00NmI2YSNsbGAuMmRrLWxhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796680&l=202507152356540AAE74DCBB209424A8AB&ply_type=2&policy=2&signature=6fb72d0fde571e9e60dec07c40e78ed9&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C1D19G2Rnu8sEUem9pjD3PClUB-peOojCnRfk_zHQ_vk%7C1752623813%7C897427a0ac4a92ab03eecc1123cd403ffd08e0f518bd0d99e75567f5654517e9; Domain=.tiktok.com; Path=/; Expires=1783727813; tt_csrf_token=WXociDln-IVgUrkDUJCM2tG9uvwr2adDzHHM; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"2LidcReqjGrmVfZFjMiYiQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175814", "format": "bytevc1_540p_595297-1 - 576x1024", "_type": "video", "_version": {"version": "2025.06.30", "release_git_head": "b0187844988e557c7e1e6bb1aabd4c1176768d86", "repository": "yt-dlp/yt-dlp"}}