{"id": "7526835479208037654", "formats": [{"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "download", "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/o0LEydgbtFsmABkDjo16RngNCEQefFkDIpYiWI/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2940&bt=1470&cs=0&ds=3&ft=4KJMyMzm8Zmo0JsmPI4jVSDbQpWrKsd.&mime_type=video_mp4&qs=0&rc=OjRpO2U4aDs2NTQ0M2hlO0Bpamw6aHQ5cmhlNDMzbzczNUBeMjAwYzViNWIxMC00YjMvYSM2YF5vMmRrLm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796645&l=20250715235649006D6CE2F3262224EC44&ply_type=2&policy=2&signature=abd0a4fbf353fdbd27a979da43ee1e3d&tk=tt_chain_token", "format_note": "watermarked", "preference": -2, "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "dynamic_range": "SDR", "cookies": "ttwid=1%7CeURu_nP2HErzklDmxFV-4h6x6OTpN8dApNSxqyhViPs%7C1752623809%7C6ea4a8c165a96d411c1e0f862921c8d6e3f3762e125aacbd87d36a0de305b93f; Domain=.tiktok.com; Path=/; Expires=1783727809; tt_csrf_token=e2vpT8Fj-HlNqMEZZPm-3-xUwtQenFSFe3P0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"kYsm6y2ZtUYl/+I1rKY5Ww==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175809", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526835479208037654"}, "format": "download - unknown (watermarked)"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_1493354-0", "tbr": 1493, "quality": 1, "preference": -1, "filesize": 6788601, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/o4REFd6kMIALkMAysOsC0fgLmgE1tBDVoQjReF/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2916&bt=1458&cs=0&ds=6&ft=4KJMyMzm8Zmo0JsmPI4jVSDbQpWrKsd.&mime_type=video_mp4&qs=0&rc=ODY2ZmZmNGk5Z2hkNDY1NUBpamw6aHQ5cmhlNDMzbzczNUBfYS9iYy0zNjYxYy8wMzEwYSM2YF5vMmRrLm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796645&l=20250715235649006D6CE2F3262224EC44&ply_type=2&policy=2&signature=b837730771ce61286cae98ffa63fe547&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CeURu_nP2HErzklDmxFV-4h6x6OTpN8dApNSxqyhViPs%7C1752623809%7C6ea4a8c165a96d411c1e0f862921c8d6e3f3762e125aacbd87d36a0de305b93f; Domain=.tiktok.com; Path=/; Expires=1783727809; tt_csrf_token=e2vpT8Fj-HlNqMEZZPm-3-xUwtQenFSFe3P0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"kYsm6y2ZtUYl/+I1rKY5Ww==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175809", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526835479208037654"}, "format": "h264_540p_1493354-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_1493354-1", "tbr": 1493, "quality": 1, "preference": -1, "filesize": 6788601, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/o4REFd6kMIALkMAysOsC0fgLmgE1tBDVoQjReF/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2916&bt=1458&cs=0&ds=6&ft=4KJMyMzm8Zmo0JsmPI4jVSDbQpWrKsd.&mime_type=video_mp4&qs=0&rc=ODY2ZmZmNGk5Z2hkNDY1NUBpamw6aHQ5cmhlNDMzbzczNUBfYS9iYy0zNjYxYy8wMzEwYSM2YF5vMmRrLm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796645&l=20250715235649006D6CE2F3262224EC44&ply_type=2&policy=2&signature=b837730771ce61286cae98ffa63fe547&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CeURu_nP2HErzklDmxFV-4h6x6OTpN8dApNSxqyhViPs%7C1752623809%7C6ea4a8c165a96d411c1e0f862921c8d6e3f3762e125aacbd87d36a0de305b93f; Domain=.tiktok.com; Path=/; Expires=1783727809; tt_csrf_token=e2vpT8Fj-HlNqMEZZPm-3-xUwtQenFSFe3P0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"kYsm6y2ZtUYl/+I1rKY5Ww==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175809", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526835479208037654"}, "format": "h264_540p_1493354-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_607429-0", "tbr": 607, "quality": 1, "preference": -1, "filesize": 2761299, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oAkpRLmCoQkFERF1YBdFhDCEIjDgt2As6gfYfy/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1186&bt=593&cs=2&ds=6&ft=4KJMyMzm8Zmo0JsmPI4jVSDbQpWrKsd.&mime_type=video_mp4&qs=11&rc=ZTtkNWk2Ozs5aGQ7Ojs5O0Bpamw6aHQ5cmhlNDMzbzczNUAtMDExYTRgXzAxMi8yYmAzYSM2YF5vMmRrLm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796645&l=20250715235649006D6CE2F3262224EC44&ply_type=2&policy=2&signature=003aed1c22b95041ed8c6d1e1d72d1fa&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CeURu_nP2HErzklDmxFV-4h6x6OTpN8dApNSxqyhViPs%7C1752623809%7C6ea4a8c165a96d411c1e0f862921c8d6e3f3762e125aacbd87d36a0de305b93f; Domain=.tiktok.com; Path=/; Expires=1783727809; tt_csrf_token=e2vpT8Fj-HlNqMEZZPm-3-xUwtQenFSFe3P0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"kYsm6y2ZtUYl/+I1rKY5Ww==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175809", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526835479208037654"}, "format": "bytevc1_540p_607429-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_607429-1", "tbr": 607, "quality": 1, "preference": -1, "filesize": 2761299, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oAkpRLmCoQkFERF1YBdFhDCEIjDgt2As6gfYfy/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1186&bt=593&cs=2&ds=6&ft=4KJMyMzm8Zmo0JsmPI4jVSDbQpWrKsd.&mime_type=video_mp4&qs=11&rc=ZTtkNWk2Ozs5aGQ7Ojs5O0Bpamw6aHQ5cmhlNDMzbzczNUAtMDExYTRgXzAxMi8yYmAzYSM2YF5vMmRrLm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796645&l=20250715235649006D6CE2F3262224EC44&ply_type=2&policy=2&signature=003aed1c22b95041ed8c6d1e1d72d1fa&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CeURu_nP2HErzklDmxFV-4h6x6OTpN8dApNSxqyhViPs%7C1752623809%7C6ea4a8c165a96d411c1e0f862921c8d6e3f3762e125aacbd87d36a0de305b93f; Domain=.tiktok.com; Path=/; Expires=1783727809; tt_csrf_token=e2vpT8Fj-HlNqMEZZPm-3-xUwtQenFSFe3P0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"kYsm6y2ZtUYl/+I1rKY5Ww==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175809", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526835479208037654"}, "format": "bytevc1_540p_607429-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_758300-0", "tbr": 758, "quality": 2, "preference": -1, "filesize": 3447140, "width": 720, "height": 1280, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oMa0vADSjIYH2BZRVNi2wiZQEEs5BuUBSS6TS/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1480&bt=740&cs=2&ds=3&ft=4KJMyMzm8Zmo0JsmPI4jVSDbQpWrKsd.&mime_type=video_mp4&qs=14&rc=NDU6ZDM6NzM5NTtnaTk7O0Bpamw6aHQ5cmhlNDMzbzczNUAwYV9gM2A2XmAxM2NhNDAuYSM2YF5vMmRrLm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796645&l=20250715235649006D6CE2F3262224EC44&ply_type=2&policy=2&signature=f664f011bc4c072dd9d7dea19749ea6a&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CeURu_nP2HErzklDmxFV-4h6x6OTpN8dApNSxqyhViPs%7C1752623809%7C6ea4a8c165a96d411c1e0f862921c8d6e3f3762e125aacbd87d36a0de305b93f; Domain=.tiktok.com; Path=/; Expires=1783727809; tt_csrf_token=e2vpT8Fj-HlNqMEZZPm-3-xUwtQenFSFe3P0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"kYsm6y2ZtUYl/+I1rKY5Ww==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175809", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526835479208037654"}, "format": "bytevc1_720p_758300-0 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_758300-1", "tbr": 758, "quality": 2, "preference": -1, "filesize": 3447140, "width": 720, "height": 1280, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oMa0vADSjIYH2BZRVNi2wiZQEEs5BuUBSS6TS/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1480&bt=740&cs=2&ds=3&ft=4KJMyMzm8Zmo0JsmPI4jVSDbQpWrKsd.&mime_type=video_mp4&qs=14&rc=NDU6ZDM6NzM5NTtnaTk7O0Bpamw6aHQ5cmhlNDMzbzczNUAwYV9gM2A2XmAxM2NhNDAuYSM2YF5vMmRrLm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796645&l=20250715235649006D6CE2F3262224EC44&ply_type=2&policy=2&signature=f664f011bc4c072dd9d7dea19749ea6a&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CeURu_nP2HErzklDmxFV-4h6x6OTpN8dApNSxqyhViPs%7C1752623809%7C6ea4a8c165a96d411c1e0f862921c8d6e3f3762e125aacbd87d36a0de305b93f; Domain=.tiktok.com; Path=/; Expires=1783727809; tt_csrf_token=e2vpT8Fj-HlNqMEZZPm-3-xUwtQenFSFe3P0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"kYsm6y2ZtUYl/+I1rKY5Ww==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175809", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526835479208037654"}, "format": "bytevc1_720p_758300-1 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_1506777-0", "tbr": 1506, "quality": 3, "preference": -1, "filesize": 6844161, "width": 1080, "height": 1920, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/owvZ2YRa5BsN62mBhsEwI0uTZSStuHBiQEGAi/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2942&bt=1471&cs=2&ds=4&ft=4KJMyMzm8Zmo0JsmPI4jVSDbQpWrKsd.&mime_type=video_mp4&qs=15&rc=Ozo0PDU1aDc2ZTY0aGUzNkBpamw6aHQ5cmhlNDMzbzczNUBjNTBhNTBhNjIxM2FhMTA1YSM2YF5vMmRrLm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796645&l=20250715235649006D6CE2F3262224EC44&ply_type=2&policy=2&signature=d56d55ed53d2d4762ba449f813a3a6b7&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CeURu_nP2HErzklDmxFV-4h6x6OTpN8dApNSxqyhViPs%7C1752623809%7C6ea4a8c165a96d411c1e0f862921c8d6e3f3762e125aacbd87d36a0de305b93f; Domain=.tiktok.com; Path=/; Expires=1783727809; tt_csrf_token=e2vpT8Fj-HlNqMEZZPm-3-xUwtQenFSFe3P0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"kYsm6y2ZtUYl/+I1rKY5Ww==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175809", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526835479208037654"}, "format": "bytevc1_1080p_1506777-0 - 1080x1920"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_1506777-1", "tbr": 1506, "quality": 3, "preference": -1, "filesize": 6844161, "width": 1080, "height": 1920, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/owvZ2YRa5BsN62mBhsEwI0uTZSStuHBiQEGAi/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2942&bt=1471&cs=2&ds=4&ft=4KJMyMzm8Zmo0JsmPI4jVSDbQpWrKsd.&mime_type=video_mp4&qs=15&rc=Ozo0PDU1aDc2ZTY0aGUzNkBpamw6aHQ5cmhlNDMzbzczNUBjNTBhNTBhNjIxM2FhMTA1YSM2YF5vMmRrLm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796645&l=20250715235649006D6CE2F3262224EC44&ply_type=2&policy=2&signature=d56d55ed53d2d4762ba449f813a3a6b7&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CeURu_nP2HErzklDmxFV-4h6x6OTpN8dApNSxqyhViPs%7C1752623809%7C6ea4a8c165a96d411c1e0f862921c8d6e3f3762e125aacbd87d36a0de305b93f; Domain=.tiktok.com; Path=/; Expires=1783727809; tt_csrf_token=e2vpT8Fj-HlNqMEZZPm-3-xUwtQenFSFe3P0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"kYsm6y2ZtUYl/+I1rKY5Ww==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175809", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526835479208037654"}, "format": "bytevc1_1080p_1506777-1 - 1080x1920"}], "subtitles": {}, "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526835479208037654"}, "channel": "<PERSON>", "channel_id": "MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader": "dylan.page", "uploader_id": "6760368158794155013", "channel_url": "https://www.tiktok.com/@MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader_url": "https://www.tiktok.com/@dylan.page", "track": "original sound", "artists": ["<PERSON>"], "duration": 36, "title": "<PERSON><PERSON> covered <PERSON> too 😭🥀", "description": "<PERSON><PERSON> covered <PERSON> too 😭🥀", "timestamp": 1752477958, "view_count": 10000000, "like_count": 1200000, "repost_count": 54900, "comment_count": 12400, "thumbnails": [{"id": "dynamicCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/okQLjDdkIAkF4eAE2FsEmkEAXEBRgAFUo0CrHf~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=sw%2BIdgnSURS4VNEN2s7KuyfbcZQ%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -2}, {"id": "cover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/okQLjDdkIAkF4eAE2FsEmkEAXEBRgAFUo0CrHf~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=sw%2BIdgnSURS4VNEN2s7KuyfbcZQ%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}, {"id": "originCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oAsRS2ANvBR5B6Eco0iah2ThiBHSBQuZI4wIY~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=ul8N5go3X%2FtxSZGNvPROeeyd1Jc%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}], "webpage_url": "https://www.tiktok.com/@dylan.page/video/7526835479208037654", "webpage_url_basename": "7526835479208037654", "webpage_url_domain": "tiktok.com", "extractor": "TikTok", "extractor_key": "TikTok", "thumbnail": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oAsRS2ANvBR5B6Eco0iah2ThiBHSBQuZI4wIY~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=ul8N5go3X%2FtxSZGNvPROeeyd1Jc%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "display_id": "7526835479208037654", "fulltitle": "<PERSON><PERSON> covered <PERSON> too 😭🥀", "duration_string": "36", "upload_date": "20250714", "artist": "<PERSON>", "epoch": 1752623809, "ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_607429-1", "tbr": 607, "quality": 1, "preference": -1, "filesize": 2761299, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oAkpRLmCoQkFERF1YBdFhDCEIjDgt2As6gfYfy/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1186&bt=593&cs=2&ds=6&ft=4KJMyMzm8Zmo0JsmPI4jVSDbQpWrKsd.&mime_type=video_mp4&qs=11&rc=ZTtkNWk2Ozs5aGQ7Ojs5O0Bpamw6aHQ5cmhlNDMzbzczNUAtMDExYTRgXzAxMi8yYmAzYSM2YF5vMmRrLm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796645&l=20250715235649006D6CE2F3262224EC44&ply_type=2&policy=2&signature=003aed1c22b95041ed8c6d1e1d72d1fa&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CeURu_nP2HErzklDmxFV-4h6x6OTpN8dApNSxqyhViPs%7C1752623809%7C6ea4a8c165a96d411c1e0f862921c8d6e3f3762e125aacbd87d36a0de305b93f; Domain=.tiktok.com; Path=/; Expires=1783727809; tt_csrf_token=e2vpT8Fj-HlNqMEZZPm-3-xUwtQenFSFe3P0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"kYsm6y2ZtUYl/+I1rKY5Ww==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175809", "format": "bytevc1_540p_607429-1 - 576x1024", "_type": "video", "_version": {"version": "2025.06.30", "release_git_head": "b0187844988e557c7e1e6bb1aabd4c1176768d86", "repository": "yt-dlp/yt-dlp"}}