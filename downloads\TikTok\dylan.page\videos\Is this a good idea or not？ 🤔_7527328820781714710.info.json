{"id": "7527328820781714710", "formats": [{"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "download", "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oMfoTMRzEMDdEsZED4FF6QyeFqdzIgBunXgkQA/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2622&bt=1311&cs=0&ds=3&ft=4KJMyMzm8Zmo0A_mPI4jVVrbQpWrKsd.&mime_type=video_mp4&qs=0&rc=ZDo8ZWhpaDtlPDhnNjM2ZkBpamU7OXE5cng6NDMzbzczNUA2YDBgYDQzXzUxXy4xMzI0YSNuYGAxMmRrL29hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796699&l=202507152356464B6CF11E518C082986B0&ply_type=2&policy=2&signature=497f6421f390889c5bdc7f11ddda843f&tk=tt_chain_token", "format_note": "watermarked", "preference": -2, "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "dynamic_range": "SDR", "cookies": "ttwid=1%7Cgcui-8nNwOstz0JcOuxVZGof9NDt0rsDKXzs7NMvqfw%7C1752623806%7C4635eab35ac78116f4d2147a61d7b25378e64b884fd1d6e4f665f48dfe82e68c; Domain=.tiktok.com; Path=/; Expires=1783727806; tt_csrf_token=s1KG30Mi-mw0jL-37g-l07Lg9MxNyQv7LUkQ; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"q92Z9RQ/e4BIEWdZfbqWHg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175806", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7527328820781714710"}, "format": "download - unknown (watermarked)"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_1319746-0", "tbr": 1319, "quality": 1, "preference": -1, "filesize": 15352781, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oozAOGevB2r0iCA3Iid1IcLBwiXCyqQZIiOJEp/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2576&bt=1288&cs=0&ds=6&ft=4KJMyMzm8Zmo0A_mPI4jVVrbQpWrKsd.&mime_type=video_mp4&qs=0&rc=NDZpODw0Ozs3ZWdoMzk1ZUBpamU7OXE5cng6NDMzbzczNUBjXzA0LTE2Xi8xXi1jNmEtYSNuYGAxMmRrL29hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796699&l=202507152356464B6CF11E518C082986B0&ply_type=2&policy=2&signature=14f1a34a0011506f92b39d879ae21af9&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cgcui-8nNwOstz0JcOuxVZGof9NDt0rsDKXzs7NMvqfw%7C1752623806%7C4635eab35ac78116f4d2147a61d7b25378e64b884fd1d6e4f665f48dfe82e68c; Domain=.tiktok.com; Path=/; Expires=1783727806; tt_csrf_token=s1KG30Mi-mw0jL-37g-l07Lg9MxNyQv7LUkQ; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"q92Z9RQ/e4BIEWdZfbqWHg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175806", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7527328820781714710"}, "format": "h264_540p_1319746-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_1319746-1", "tbr": 1319, "quality": 1, "preference": -1, "filesize": 15352781, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oozAOGevB2r0iCA3Iid1IcLBwiXCyqQZIiOJEp/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2576&bt=1288&cs=0&ds=6&ft=4KJMyMzm8Zmo0A_mPI4jVVrbQpWrKsd.&mime_type=video_mp4&qs=0&rc=NDZpODw0Ozs3ZWdoMzk1ZUBpamU7OXE5cng6NDMzbzczNUBjXzA0LTE2Xi8xXi1jNmEtYSNuYGAxMmRrL29hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796699&l=202507152356464B6CF11E518C082986B0&ply_type=2&policy=2&signature=14f1a34a0011506f92b39d879ae21af9&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cgcui-8nNwOstz0JcOuxVZGof9NDt0rsDKXzs7NMvqfw%7C1752623806%7C4635eab35ac78116f4d2147a61d7b25378e64b884fd1d6e4f665f48dfe82e68c; Domain=.tiktok.com; Path=/; Expires=1783727806; tt_csrf_token=s1KG30Mi-mw0jL-37g-l07Lg9MxNyQv7LUkQ; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"q92Z9RQ/e4BIEWdZfbqWHg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175806", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7527328820781714710"}, "format": "h264_540p_1319746-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_526407-0", "tbr": 526, "quality": 1, "preference": -1, "filesize": 6123762, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/owI0t5iiFA13QIAAwIi1OdyBevZLGGziCCAx2E/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1028&bt=514&cs=2&ds=6&ft=4KJMyMzm8Zmo0A_mPI4jVVrbQpWrKsd.&mime_type=video_mp4&qs=11&rc=NDVpZ2c7ZGZlaWQ2aGZpZkBpamU7OXE5cng6NDMzbzczNUA0NTFjYjUxNjUxNDAvY2EyYSNuYGAxMmRrL29hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796699&l=202507152356464B6CF11E518C082986B0&ply_type=2&policy=2&signature=5acd6b2b5ea14b4f046e43c3ea74554f&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cgcui-8nNwOstz0JcOuxVZGof9NDt0rsDKXzs7NMvqfw%7C1752623806%7C4635eab35ac78116f4d2147a61d7b25378e64b884fd1d6e4f665f48dfe82e68c; Domain=.tiktok.com; Path=/; Expires=1783727806; tt_csrf_token=s1KG30Mi-mw0jL-37g-l07Lg9MxNyQv7LUkQ; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"q92Z9RQ/e4BIEWdZfbqWHg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175806", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7527328820781714710"}, "format": "bytevc1_540p_526407-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_526407-1", "tbr": 526, "quality": 1, "preference": -1, "filesize": 6123762, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/owI0t5iiFA13QIAAwIi1OdyBevZLGGziCCAx2E/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1028&bt=514&cs=2&ds=6&ft=4KJMyMzm8Zmo0A_mPI4jVVrbQpWrKsd.&mime_type=video_mp4&qs=11&rc=NDVpZ2c7ZGZlaWQ2aGZpZkBpamU7OXE5cng6NDMzbzczNUA0NTFjYjUxNjUxNDAvY2EyYSNuYGAxMmRrL29hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796699&l=202507152356464B6CF11E518C082986B0&ply_type=2&policy=2&signature=5acd6b2b5ea14b4f046e43c3ea74554f&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cgcui-8nNwOstz0JcOuxVZGof9NDt0rsDKXzs7NMvqfw%7C1752623806%7C4635eab35ac78116f4d2147a61d7b25378e64b884fd1d6e4f665f48dfe82e68c; Domain=.tiktok.com; Path=/; Expires=1783727806; tt_csrf_token=s1KG30Mi-mw0jL-37g-l07Lg9MxNyQv7LUkQ; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"q92Z9RQ/e4BIEWdZfbqWHg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175806", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7527328820781714710"}, "format": "bytevc1_540p_526407-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_636086-0", "tbr": 636, "quality": 2, "preference": -1, "filesize": 7399676, "width": 720, "height": 1280, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/ocgsRoOQdTzEE6ekFAIHXuFf6EvgCFQDgMkBqn/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1242&bt=621&cs=2&ds=3&ft=4KJMyMzm8Zmo0A_mPI4jVVrbQpWrKsd.&mime_type=video_mp4&qs=14&rc=ODQzNmg0Mzs2ZzQ5aDtoN0BpamU7OXE5cng6NDMzbzczNUA1NS1iMzNeNS0xYWMuYDUxYSNuYGAxMmRrL29hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796699&l=202507152356464B6CF11E518C082986B0&ply_type=2&policy=2&signature=39af23bea6668a9a5e08b39ceb074832&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cgcui-8nNwOstz0JcOuxVZGof9NDt0rsDKXzs7NMvqfw%7C1752623806%7C4635eab35ac78116f4d2147a61d7b25378e64b884fd1d6e4f665f48dfe82e68c; Domain=.tiktok.com; Path=/; Expires=1783727806; tt_csrf_token=s1KG30Mi-mw0jL-37g-l07Lg9MxNyQv7LUkQ; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"q92Z9RQ/e4BIEWdZfbqWHg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175806", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7527328820781714710"}, "format": "bytevc1_720p_636086-0 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_636086-1", "tbr": 636, "quality": 2, "preference": -1, "filesize": 7399676, "width": 720, "height": 1280, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/ocgsRoOQdTzEE6ekFAIHXuFf6EvgCFQDgMkBqn/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1242&bt=621&cs=2&ds=3&ft=4KJMyMzm8Zmo0A_mPI4jVVrbQpWrKsd.&mime_type=video_mp4&qs=14&rc=ODQzNmg0Mzs2ZzQ5aDtoN0BpamU7OXE5cng6NDMzbzczNUA1NS1iMzNeNS0xYWMuYDUxYSNuYGAxMmRrL29hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796699&l=202507152356464B6CF11E518C082986B0&ply_type=2&policy=2&signature=39af23bea6668a9a5e08b39ceb074832&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cgcui-8nNwOstz0JcOuxVZGof9NDt0rsDKXzs7NMvqfw%7C1752623806%7C4635eab35ac78116f4d2147a61d7b25378e64b884fd1d6e4f665f48dfe82e68c; Domain=.tiktok.com; Path=/; Expires=1783727806; tt_csrf_token=s1KG30Mi-mw0jL-37g-l07Lg9MxNyQv7LUkQ; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"q92Z9RQ/e4BIEWdZfbqWHg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175806", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7527328820781714710"}, "format": "bytevc1_720p_636086-1 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_1032756-0", "tbr": 1032, "quality": 3, "preference": -1, "filesize": 12014180, "width": 1080, "height": 1920, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/o0213iAIyB0ROiCidwLZA8FGIvLeIvCiMYEdzQ/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2016&bt=1008&cs=2&ds=4&ft=4KJMyMzm8Zmo0A_mPI4jVVrbQpWrKsd.&mime_type=video_mp4&qs=15&rc=Ozw7Ojc6PDw1O2ZnODxpOkBpamU7OXE5cng6NDMzbzczNUAuNS9iYmM2Xy4xYi9gMy42YSNuYGAxMmRrL29hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796699&l=202507152356464B6CF11E518C082986B0&ply_type=2&policy=2&signature=86e5e493ce8c3284571335231be6712b&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cgcui-8nNwOstz0JcOuxVZGof9NDt0rsDKXzs7NMvqfw%7C1752623806%7C4635eab35ac78116f4d2147a61d7b25378e64b884fd1d6e4f665f48dfe82e68c; Domain=.tiktok.com; Path=/; Expires=1783727806; tt_csrf_token=s1KG30Mi-mw0jL-37g-l07Lg9MxNyQv7LUkQ; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"q92Z9RQ/e4BIEWdZfbqWHg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175806", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7527328820781714710"}, "format": "bytevc1_1080p_1032756-0 - 1080x1920"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_1032756-1", "tbr": 1032, "quality": 3, "preference": -1, "filesize": 12014180, "width": 1080, "height": 1920, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/o0213iAIyB0ROiCidwLZA8FGIvLeIvCiMYEdzQ/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2016&bt=1008&cs=2&ds=4&ft=4KJMyMzm8Zmo0A_mPI4jVVrbQpWrKsd.&mime_type=video_mp4&qs=15&rc=Ozw7Ojc6PDw1O2ZnODxpOkBpamU7OXE5cng6NDMzbzczNUAuNS9iYmM2Xy4xYi9gMy42YSNuYGAxMmRrL29hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796699&l=202507152356464B6CF11E518C082986B0&ply_type=2&policy=2&signature=86e5e493ce8c3284571335231be6712b&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cgcui-8nNwOstz0JcOuxVZGof9NDt0rsDKXzs7NMvqfw%7C1752623806%7C4635eab35ac78116f4d2147a61d7b25378e64b884fd1d6e4f665f48dfe82e68c; Domain=.tiktok.com; Path=/; Expires=1783727806; tt_csrf_token=s1KG30Mi-mw0jL-37g-l07Lg9MxNyQv7LUkQ; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"q92Z9RQ/e4BIEWdZfbqWHg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175806", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7527328820781714710"}, "format": "bytevc1_1080p_1032756-1 - 1080x1920"}], "subtitles": {}, "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7527328820781714710"}, "channel": "<PERSON>", "channel_id": "MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader": "dylan.page", "uploader_id": "6760368158794155013", "channel_url": "https://www.tiktok.com/@MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader_url": "https://www.tiktok.com/@dylan.page", "track": "original sound", "artists": ["<PERSON>"], "duration": 93, "title": "Is this a good idea or not? 🤔", "description": "Is this a good idea or not? 🤔", "timestamp": 1752592820, "view_count": 1700000, "like_count": 191600, "repost_count": 10500, "comment_count": 5061, "thumbnails": [{"id": "dynamicCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oQk2Izqq0ESACAC9cBOiHBjiIOBAdOACZwf1bm~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=Y07mjK%2FOHtG7hX9i0jcZuPo166E%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -2}, {"id": "cover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oQk2Izqq0ESACAC9cBOiHBjiIOBAdOACZwf1bm~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=Y07mjK%2FOHtG7hX9i0jcZuPo166E%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}, {"id": "originCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oMACBbL2YI0IvAw83tBzGIeDyOcIiiiwCoiC1Z~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=IyNF1BwWwjR780S5dnqHFMhWYWc%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}], "webpage_url": "https://www.tiktok.com/@dylan.page/video/7527328820781714710", "webpage_url_basename": "7527328820781714710", "webpage_url_domain": "tiktok.com", "extractor": "TikTok", "extractor_key": "TikTok", "thumbnail": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oMACBbL2YI0IvAw83tBzGIeDyOcIiiiwCoiC1Z~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=IyNF1BwWwjR780S5dnqHFMhWYWc%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "display_id": "7527328820781714710", "fulltitle": "Is this a good idea or not? 🤔", "duration_string": "1:33", "upload_date": "20250715", "artist": "<PERSON>", "epoch": 1752623806, "ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_526407-1", "tbr": 526, "quality": 1, "preference": -1, "filesize": 6123762, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/owI0t5iiFA13QIAAwIi1OdyBevZLGGziCCAx2E/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1028&bt=514&cs=2&ds=6&ft=4KJMyMzm8Zmo0A_mPI4jVVrbQpWrKsd.&mime_type=video_mp4&qs=11&rc=NDVpZ2c7ZGZlaWQ2aGZpZkBpamU7OXE5cng6NDMzbzczNUA0NTFjYjUxNjUxNDAvY2EyYSNuYGAxMmRrL29hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796699&l=202507152356464B6CF11E518C082986B0&ply_type=2&policy=2&signature=5acd6b2b5ea14b4f046e43c3ea74554f&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cgcui-8nNwOstz0JcOuxVZGof9NDt0rsDKXzs7NMvqfw%7C1752623806%7C4635eab35ac78116f4d2147a61d7b25378e64b884fd1d6e4f665f48dfe82e68c; Domain=.tiktok.com; Path=/; Expires=1783727806; tt_csrf_token=s1KG30Mi-mw0jL-37g-l07Lg9MxNyQv7LUkQ; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"q92Z9RQ/e4BIEWdZfbqWHg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175806", "format": "bytevc1_540p_526407-1 - 576x1024", "_type": "video", "_version": {"version": "2025.06.30", "release_git_head": "b0187844988e557c7e1e6bb1aabd4c1176768d86", "repository": "yt-dlp/yt-dlp"}}