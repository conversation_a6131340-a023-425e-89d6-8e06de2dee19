#!/usr/bin/env python3
"""
Social Media Auto Tracker Web Dashboard
User-friendly web interface for managing social media downloads
"""
import os
import json
import sqlite3
import subprocess
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional

from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash
from werkzeug.security import check_password_hash, generate_password_hash
import config
from auto_tracker import AutoTracker

# Install Flask if not available
try:
    import flask
except ImportError:
    print("Installing Flask...")
    import subprocess
    subprocess.check_call(['pip', 'install', 'flask'])

app = Flask(__name__)
app.secret_key = 'social_tracker_secret_key_2025'

# Configuration
DASHBOARD_USERNAME = "ezek"
DASHBOARD_PASSWORD = "5031Bryan!"
DASHBOARD_HOST = "*************"
DASHBOARD_PORT = 9990

# Global tracker instance
tracker = None
daemon_thread = None
daemon_running = False

def init_tracker():
    """Initialize the auto tracker"""
    global tracker
    if tracker is None:
        tracker = AutoTracker()
    return tracker

def check_auth():
    """Check if user is authenticated"""
    return session.get('authenticated') == True

def require_auth(f):
    """Decorator to require authentication"""
    def decorated_function(*args, **kwargs):
        if not check_auth():
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        if username == DASHBOARD_USERNAME and password == DASHBOARD_PASSWORD:
            session['authenticated'] = True
            flash('Login successful!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid credentials!', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """Logout"""
    session.pop('authenticated', None)
    flash('Logged out successfully!', 'info')
    return redirect(url_for('login'))

@app.route('/')
@require_auth
def dashboard():
    """Main dashboard"""
    tracker = init_tracker()
    
    # Get system status
    users = tracker.list_users()
    stats = tracker.get_stats()
    
    # Get daemon status
    daemon_status = get_daemon_status()
    
    # Get recent downloads
    recent_downloads = get_recent_downloads()
    
    return render_template('dashboard.html', 
                         users=users, 
                         stats=stats,
                         daemon_status=daemon_status,
                         recent_downloads=recent_downloads)

@app.route('/users')
@require_auth
def users():
    """Users management page"""
    tracker = init_tracker()
    users = tracker.list_users()
    
    # Group users by platform
    users_by_platform = {}
    for user in users:
        platform = user['platform']
        if platform not in users_by_platform:
            users_by_platform[platform] = []
        users_by_platform[platform].append(user)
    
    return render_template('users.html', users_by_platform=users_by_platform)

@app.route('/logs')
@require_auth
def logs():
    """Download logs page"""
    recent_downloads = get_recent_downloads(limit=100)
    return render_template('logs.html', downloads=recent_downloads)

@app.route('/settings')
@require_auth
def settings():
    """Settings page"""
    daemon_status = get_daemon_status()
    return render_template('settings.html', 
                         daemon_status=daemon_status,
                         current_interval=config.DEFAULT_CHECK_INTERVAL)

# API Routes
@app.route('/api/add_user', methods=['POST'])
@require_auth
def api_add_user():
    """Add a new user"""
    try:
        data = request.json
        platform = data['platform']
        username = data['username']
        
        tracker = init_tracker()
        success = tracker.add_user(platform, username)
        
        if success:
            return jsonify({'success': True, 'message': f'Added {platform} user @{username}'})
        else:
            return jsonify({'success': False, 'message': 'Failed to add user'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/remove_user', methods=['POST'])
@require_auth
def api_remove_user():
    """Remove a user"""
    try:
        data = request.json
        platform = data['platform']
        username = data['username']
        
        tracker = init_tracker()
        success = tracker.remove_user(platform, username)
        
        if success:
            return jsonify({'success': True, 'message': f'Removed {platform} user @{username}'})
        else:
            return jsonify({'success': False, 'message': 'Failed to remove user'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/check_user', methods=['POST'])
@require_auth
def api_check_user():
    """Manual check for a user"""
    try:
        data = request.json
        platform = data['platform']
        username = data['username']
        
        tracker = init_tracker()
        result = tracker.check_user_for_new_content(platform, username)
        
        return jsonify({'success': True, 'result': result})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/check_all_users', methods=['POST'])
@require_auth
def api_check_all_users():
    """Manual check for all users"""
    try:
        tracker = init_tracker()
        result = tracker.check_all_users()
        
        return jsonify({'success': True, 'result': result})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/daemon_control', methods=['POST'])
@require_auth
def api_daemon_control():
    """Control daemon (start/stop)"""
    try:
        data = request.json
        action = data['action']
        
        if action == 'start':
            interval = data.get('interval', config.DEFAULT_CHECK_INTERVAL)
            success = start_daemon(interval)
            message = 'Daemon started' if success else 'Failed to start daemon'
        elif action == 'stop':
            success = stop_daemon()
            message = 'Daemon stopped' if success else 'Failed to stop daemon'
        else:
            return jsonify({'success': False, 'message': 'Invalid action'})
        
        return jsonify({'success': success, 'message': message})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/status')
@require_auth
def api_status():
    """Get system status"""
    try:
        tracker = init_tracker()
        users = tracker.list_users()
        stats = tracker.get_stats()
        daemon_status = get_daemon_status()
        
        return jsonify({
            'success': True,
            'users': len(users),
            'stats': stats,
            'daemon': daemon_status
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

def get_daemon_status():
    """Get daemon status"""
    global daemon_running, daemon_thread
    
    return {
        'running': daemon_running,
        'thread_alive': daemon_thread.is_alive() if daemon_thread else False,
        'interval': config.DEFAULT_CHECK_INTERVAL,
        'last_check': get_last_check_time()
    }

def get_last_check_time():
    """Get last check time from database"""
    try:
        tracker = init_tracker()
        with sqlite3.connect(tracker.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT MAX(start_time) FROM check_sessions
            ''')
            result = cursor.fetchone()
            return result[0] if result and result[0] else None
    except:
        return None

def get_recent_downloads(limit=50):
    """Get recent downloads from database"""
    try:
        tracker = init_tracker()
        with sqlite3.connect(tracker.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT dc.*, u.platform, u.username, cs.start_time
                FROM downloaded_content dc
                JOIN users u ON dc.user_id = u.id
                JOIN check_sessions cs ON dc.session_id = cs.id
                ORDER BY dc.download_time DESC
                LIMIT ?
            ''', (limit,))
            
            downloads = []
            for row in cursor.fetchall():
                downloads.append({
                    'id': row[0],
                    'content_id': row[2],
                    'content_type': row[3],
                    'file_path': row[4],
                    'download_time': row[5],
                    'platform': row[6],
                    'username': row[7],
                    'session_start': row[8]
                })
            
            return downloads
    except Exception as e:
        print(f"Error getting recent downloads: {e}")
        return []

def start_daemon(interval):
    """Start the daemon thread"""
    global daemon_thread, daemon_running
    
    if daemon_running:
        return False
    
    def daemon_worker():
        global daemon_running
        daemon_running = True
        tracker = init_tracker()
        
        while daemon_running:
            try:
                print(f"[{datetime.now()}] Running scheduled check...")
                tracker.check_all_users()
                
                # Sleep for the specified interval
                for _ in range(interval):
                    if not daemon_running:
                        break
                    time.sleep(1)
                    
            except Exception as e:
                print(f"Daemon error: {e}")
                time.sleep(60)  # Wait 1 minute on error
    
    daemon_thread = threading.Thread(target=daemon_worker, daemon=True)
    daemon_thread.start()
    return True

def stop_daemon():
    """Stop the daemon thread"""
    global daemon_running
    daemon_running = False
    return True

if __name__ == '__main__':
    print(f"🌐 Starting Social Media Tracker Dashboard")
    print(f"📍 URL: http://{DASHBOARD_HOST}:{DASHBOARD_PORT}")
    print(f"👤 Username: {DASHBOARD_USERNAME}")
    print(f"🔑 Password: {DASHBOARD_PASSWORD}")
    print("=" * 50)
    
    app.run(host=DASHBOARD_HOST, port=DASHBOARD_PORT, debug=False)
