# 🔐 Instagram Authentication Setup Guide

This guide explains how to overcome Instagram's rate limiting and authentication issues for reliable downloading.

## 🚨 **Why Instagram Authentication is Needed**

Instagram has strict anti-bot measures that cause these issues:
- **Rate limiting**: "Please wait a few minutes before you try again"
- **403 Forbidden**: Blocked requests without authentication
- **401 Unauthorized**: Login required for most content
- **IP blocking**: Temporary bans for excessive requests

## ✅ **Solution: Session File Authentication (Recommended)**

The most reliable method is using session files from real browser logins.

### **Step 1: Create Instagram Session**

Run the session creator script:

```bash
python3 create_instagram_session.py
```

This will:
1. Ask for your Instagram username/password
2. Login to Instagram safely
3. Save a session file to `~/.social_tracker/sessions/`
4. Provide instructions for using the session

### **Step 2: Configure Session File**

Edit `config.py` and add your session file path:

```python
# Instagram authentication (choose one method)
INSTAGRAM_SESSION_FILE = "/home/<USER>/.social_tracker/sessions/your_username_session"
```

### **Step 3: Test Authentication**

Test that Instagram authentication works:

```bash
python3 auto_tracker.py add --platform instagram --username nasa
python3 auto_tracker.py check --platform instagram --username nasa
```

## 🔧 **Alternative Methods**

### **Method 2: Username/Password (Less Reliable)**

If session files don't work, you can use direct credentials:

```python
# In config.py
INSTAGRAM_USERNAME = "your_username"
INSTAGRAM_PASSWORD = "your_password"
```

⚠️ **Warning**: This method is more likely to trigger rate limits.

### **Method 3: Proxy Rotation (Advanced)**

For heavy usage, consider using rotating proxies:

```python
# In config.py
INSTAGRAM_PROXIES = [
    "http://proxy1:port",
    "http://proxy2:port",
    "http://proxy3:port"
]
```

## 🛡️ **Best Practices for Avoiding Blocks**

### **1. Rate Limiting**
- **Delay between requests**: 2-10 seconds
- **Daily limits**: Max 200 requests per hour
- **Respect 429 errors**: Back off when rate limited

### **2. Realistic Behavior**
- **Human-like patterns**: Vary request timing
- **Don't mass download**: Spread downloads over time
- **Use session files**: Avoid repeated logins

### **3. Account Safety**
- **Use dedicated accounts**: Don't use your main Instagram account
- **Enable 2FA carefully**: Can complicate automation
- **Monitor for warnings**: Instagram may send security alerts

## 🔄 **Session File Management**

### **Session Expiration**
- Sessions expire after ~60 days of inactivity
- You'll need to recreate sessions periodically
- The system will warn you when sessions expire

### **Multiple Accounts**
Create separate sessions for different accounts:

```bash
python3 create_instagram_session.py  # Account 1
# Enter different username for Account 2
python3 create_instagram_session.py  # Account 2
```

### **Session Security**
- Keep session files private (they contain login tokens)
- Don't share session files between machines
- Delete old/unused session files

## 🚀 **Production Deployment Tips**

### **For Ubuntu Servers**

1. **Create session on your local machine** (with GUI browser)
2. **Transfer session file** to server securely
3. **Update config.py** with correct session path
4. **Test authentication** before starting daemon

### **Automated Session Renewal**

Create a cron job to recreate sessions monthly:

```bash
# Add to crontab
0 0 1 * * /path/to/create_instagram_session.py
```

### **Monitoring Authentication**

Check logs for authentication issues:

```bash
tail -f ~/.social_tracker/logs/auto_tracker.log | grep -i instagram
```

## 🔍 **Troubleshooting**

### **"Session file not found"**
- Run `python3 create_instagram_session.py`
- Check file path in config.py
- Ensure file permissions are correct

### **"Invalid session"**
- Session expired, recreate it
- Instagram changed security, need new session
- Try logging in manually first

### **"Two-factor authentication required"**
- Temporarily disable 2FA
- Use app-specific passwords if available
- Consider using SMS-based 2FA instead of authenticator apps

### **Still getting rate limited**
- Increase delays between requests
- Use fewer concurrent downloads
- Consider using proxies
- Switch to session files if using credentials

## 📊 **Expected Performance**

With proper authentication:
- **Success rate**: 95%+ for public content
- **Rate limits**: Rare with proper delays
- **Daily capacity**: 1000+ posts per account
- **Reliability**: Stable for weeks without intervention

## 🎯 **Quick Setup Summary**

1. **Run**: `python3 create_instagram_session.py`
2. **Configure**: Add session file path to `config.py`
3. **Test**: `python3 auto_tracker.py check --platform instagram --username nasa`
4. **Deploy**: Start daemon with proper authentication

This setup will eliminate most Instagram authentication issues and provide reliable downloading! 🚀
