{"id": "7526919590920719638", "formats": [{"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "download", "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oUZRE8fpfAJGMOIIgXgQAPGQfW0WQ5Dwhiqegj/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2078&bt=1039&cs=0&ds=3&ft=4KJMyMzm8Zmo0VsmPI4jVwMbQpWrKsd.&mime_type=video_mp4&qs=0&rc=Ozw5ODY7OTk8ODc8ZDY8aEBpM2txdW45cmtpNDMzbzczNUAyYzYuMS1iXjYxYDMvYWEtYSMucy0wMmRzbm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796641&l=202507152356481F2245E1805F8324E4DB&ply_type=2&policy=2&signature=608b06d84d62a5e3c665e83c75f2935a&tk=tt_chain_token", "format_note": "watermarked", "preference": -2, "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "dynamic_range": "SDR", "cookies": "ttwid=1%7Cp9lUTbaFQ0Eh1AmhH-maF1Bf5DXMXVZvFxYaJa7Yccg%7C1752623807%7C3865684e558183129cbff6b293877aeffefdf38b4f98b573214093a46a72d2a5; Domain=.tiktok.com; Path=/; Expires=1783727807; tt_csrf_token=k9eC2x5c-VwQfmwWulJ2chiFYmektUDUezXw; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"33+dL9xp1hgTnsZVXHep+g==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175808", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526919590920719638"}, "format": "download - unknown (watermarked)"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_1044366-0", "tbr": 1044, "quality": 1, "preference": -1, "filesize": 4377724, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oYbWqZMTWIiP0fppWGAEAGfRegXQIQg8AhfwGQ/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2038&bt=1019&cs=0&ds=6&ft=4KJMyMzm8Zmo0VsmPI4jVwMbQpWrKsd.&mime_type=video_mp4&qs=0&rc=ODU3Zzk8Z2U2ZzdlOztpZUBpM2txdW45cmtpNDMzbzczNUBeMDM0NWItNjIxYi1eXy9jYSMucy0wMmRzbm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796641&l=202507152356481F2245E1805F8324E4DB&ply_type=2&policy=2&signature=01149f84ad1404a011f0b578d135ccfc&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cp9lUTbaFQ0Eh1AmhH-maF1Bf5DXMXVZvFxYaJa7Yccg%7C1752623807%7C3865684e558183129cbff6b293877aeffefdf38b4f98b573214093a46a72d2a5; Domain=.tiktok.com; Path=/; Expires=1783727807; tt_csrf_token=k9eC2x5c-VwQfmwWulJ2chiFYmektUDUezXw; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"33+dL9xp1hgTnsZVXHep+g==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175808", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526919590920719638"}, "format": "h264_540p_1044366-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_1044366-1", "tbr": 1044, "quality": 1, "preference": -1, "filesize": 4377724, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oYbWqZMTWIiP0fppWGAEAGfRegXQIQg8AhfwGQ/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2038&bt=1019&cs=0&ds=6&ft=4KJMyMzm8Zmo0VsmPI4jVwMbQpWrKsd.&mime_type=video_mp4&qs=0&rc=ODU3Zzk8Z2U2ZzdlOztpZUBpM2txdW45cmtpNDMzbzczNUBeMDM0NWItNjIxYi1eXy9jYSMucy0wMmRzbm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796641&l=202507152356481F2245E1805F8324E4DB&ply_type=2&policy=2&signature=01149f84ad1404a011f0b578d135ccfc&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cp9lUTbaFQ0Eh1AmhH-maF1Bf5DXMXVZvFxYaJa7Yccg%7C1752623807%7C3865684e558183129cbff6b293877aeffefdf38b4f98b573214093a46a72d2a5; Domain=.tiktok.com; Path=/; Expires=1783727807; tt_csrf_token=k9eC2x5c-VwQfmwWulJ2chiFYmektUDUezXw; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"33+dL9xp1hgTnsZVXHep+g==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175808", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526919590920719638"}, "format": "h264_540p_1044366-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_399185-0", "tbr": 399, "quality": 1, "preference": -1, "filesize": 1673286, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oEGGqWWxgwP8Tg0eph0cAEQpAMQRIDfXMffiQA/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=778&bt=389&cs=2&ds=6&ft=4KJMyMzm8Zmo0VsmPI4jVwMbQpWrKsd.&mime_type=video_mp4&qs=11&rc=Mzw2OzxkZjNpOWdlPDo7aEBpM2txdW45cmtpNDMzbzczNUAxLmNgYy4vNTAxNGMwNTRgYSMucy0wMmRzbm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796641&l=202507152356481F2245E1805F8324E4DB&ply_type=2&policy=2&signature=e3f3b2f8490c07aaa884d9c618b82b99&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cp9lUTbaFQ0Eh1AmhH-maF1Bf5DXMXVZvFxYaJa7Yccg%7C1752623807%7C3865684e558183129cbff6b293877aeffefdf38b4f98b573214093a46a72d2a5; Domain=.tiktok.com; Path=/; Expires=1783727807; tt_csrf_token=k9eC2x5c-VwQfmwWulJ2chiFYmektUDUezXw; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"33+dL9xp1hgTnsZVXHep+g==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175808", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526919590920719638"}, "format": "bytevc1_540p_399185-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_399185-1", "tbr": 399, "quality": 1, "preference": -1, "filesize": 1673286, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oEGGqWWxgwP8Tg0eph0cAEQpAMQRIDfXMffiQA/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=778&bt=389&cs=2&ds=6&ft=4KJMyMzm8Zmo0VsmPI4jVwMbQpWrKsd.&mime_type=video_mp4&qs=11&rc=Mzw2OzxkZjNpOWdlPDo7aEBpM2txdW45cmtpNDMzbzczNUAxLmNgYy4vNTAxNGMwNTRgYSMucy0wMmRzbm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796641&l=202507152356481F2245E1805F8324E4DB&ply_type=2&policy=2&signature=e3f3b2f8490c07aaa884d9c618b82b99&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cp9lUTbaFQ0Eh1AmhH-maF1Bf5DXMXVZvFxYaJa7Yccg%7C1752623807%7C3865684e558183129cbff6b293877aeffefdf38b4f98b573214093a46a72d2a5; Domain=.tiktok.com; Path=/; Expires=1783727807; tt_csrf_token=k9eC2x5c-VwQfmwWulJ2chiFYmektUDUezXw; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"33+dL9xp1hgTnsZVXHep+g==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175808", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526919590920719638"}, "format": "bytevc1_540p_399185-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_505175-0", "tbr": 505, "quality": 2, "preference": -1, "filesize": 2117571, "width": 720, "height": 1280, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/ooPqQQEePMAsfwEGCfWgWGhgbRfAiGD0IX8kpQ/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=986&bt=493&cs=2&ds=3&ft=4KJMyMzm8Zmo0VsmPI4jVwMbQpWrKsd.&mime_type=video_mp4&qs=14&rc=NjNpPDo3MzY0Ojs1NjVmOEBpM2txdW45cmtpNDMzbzczNUAuLTI2LTQuXjYxYDIwLy0zYSMucy0wMmRzbm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796641&l=202507152356481F2245E1805F8324E4DB&ply_type=2&policy=2&signature=1a57ad6f499d0cdaa39a97c2839d49d9&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cp9lUTbaFQ0Eh1AmhH-maF1Bf5DXMXVZvFxYaJa7Yccg%7C1752623807%7C3865684e558183129cbff6b293877aeffefdf38b4f98b573214093a46a72d2a5; Domain=.tiktok.com; Path=/; Expires=1783727807; tt_csrf_token=k9eC2x5c-VwQfmwWulJ2chiFYmektUDUezXw; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"33+dL9xp1hgTnsZVXHep+g==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175808", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526919590920719638"}, "format": "bytevc1_720p_505175-0 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_505175-1", "tbr": 505, "quality": 2, "preference": -1, "filesize": 2117571, "width": 720, "height": 1280, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/ooPqQQEePMAsfwEGCfWgWGhgbRfAiGD0IX8kpQ/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=986&bt=493&cs=2&ds=3&ft=4KJMyMzm8Zmo0VsmPI4jVwMbQpWrKsd.&mime_type=video_mp4&qs=14&rc=NjNpPDo3MzY0Ojs1NjVmOEBpM2txdW45cmtpNDMzbzczNUAuLTI2LTQuXjYxYDIwLy0zYSMucy0wMmRzbm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796641&l=202507152356481F2245E1805F8324E4DB&ply_type=2&policy=2&signature=1a57ad6f499d0cdaa39a97c2839d49d9&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cp9lUTbaFQ0Eh1AmhH-maF1Bf5DXMXVZvFxYaJa7Yccg%7C1752623807%7C3865684e558183129cbff6b293877aeffefdf38b4f98b573214093a46a72d2a5; Domain=.tiktok.com; Path=/; Expires=1783727807; tt_csrf_token=k9eC2x5c-VwQfmwWulJ2chiFYmektUDUezXw; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"33+dL9xp1hgTnsZVXHep+g==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175808", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526919590920719638"}, "format": "bytevc1_720p_505175-1 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_961690-0", "tbr": 961, "quality": 3, "preference": -1, "filesize": 4027678, "width": 1080, "height": 1920, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oYvWrqhIpwiMvGXfAPQwf8QJWRAGrg0gHEfeHQ/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1878&bt=939&cs=2&ds=4&ft=4KJMyMzm8Zmo0VsmPI4jVwMbQpWrKsd.&mime_type=video_mp4&qs=15&rc=NDg4OjNmOWk8N2czODZpOUBpM2txdW45cmtpNDMzbzczNUA1YV4yMl8vX2AxNWE1L2MvYSMucy0wMmRzbm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796641&l=202507152356481F2245E1805F8324E4DB&ply_type=2&policy=2&signature=307786c1e4d82c480e9b1ec074508eaa&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cp9lUTbaFQ0Eh1AmhH-maF1Bf5DXMXVZvFxYaJa7Yccg%7C1752623807%7C3865684e558183129cbff6b293877aeffefdf38b4f98b573214093a46a72d2a5; Domain=.tiktok.com; Path=/; Expires=1783727807; tt_csrf_token=k9eC2x5c-VwQfmwWulJ2chiFYmektUDUezXw; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"33+dL9xp1hgTnsZVXHep+g==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175808", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526919590920719638"}, "format": "bytevc1_1080p_961690-0 - 1080x1920"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_961690-1", "tbr": 961, "quality": 3, "preference": -1, "filesize": 4027678, "width": 1080, "height": 1920, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oYvWrqhIpwiMvGXfAPQwf8QJWRAGrg0gHEfeHQ/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1878&bt=939&cs=2&ds=4&ft=4KJMyMzm8Zmo0VsmPI4jVwMbQpWrKsd.&mime_type=video_mp4&qs=15&rc=NDg4OjNmOWk8N2czODZpOUBpM2txdW45cmtpNDMzbzczNUA1YV4yMl8vX2AxNWE1L2MvYSMucy0wMmRzbm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796641&l=202507152356481F2245E1805F8324E4DB&ply_type=2&policy=2&signature=307786c1e4d82c480e9b1ec074508eaa&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cp9lUTbaFQ0Eh1AmhH-maF1Bf5DXMXVZvFxYaJa7Yccg%7C1752623807%7C3865684e558183129cbff6b293877aeffefdf38b4f98b573214093a46a72d2a5; Domain=.tiktok.com; Path=/; Expires=1783727807; tt_csrf_token=k9eC2x5c-VwQfmwWulJ2chiFYmektUDUezXw; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"33+dL9xp1hgTnsZVXHep+g==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175808", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526919590920719638"}, "format": "bytevc1_1080p_961690-1 - 1080x1920"}], "subtitles": {}, "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526919590920719638"}, "channel": "<PERSON>", "channel_id": "MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader": "dylan.page", "uploader_id": "6760368158794155013", "channel_url": "https://www.tiktok.com/@MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader_url": "https://www.tiktok.com/@dylan.page", "track": "original sound", "artists": ["<PERSON>"], "duration": 33, "title": "This a good idea or not??🤔", "description": "This a good idea or not??🤔", "timestamp": **********, "view_count": 6200000, "like_count": 847500, "repost_count": 218600, "comment_count": 5187, "thumbnails": [{"id": "dynamicCover", "url": "https://p19-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/ogAAFEYokECAAR9Q2apfCFfdDD0JnPIvuTZrwg~tplv-tiktokx-origin.image?dr=9636&x-expires=**********&x-signature=x1xovyyop0IhHmyW6%2Bq40ix5riQ%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -2}, {"id": "cover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/ogAAFEYokECAAR9Q2apfCFfdDD0JnPIvuTZrwg~tplv-tiktokx-origin.image?dr=9636&x-expires=**********&x-signature=hzok4mSHtjnLMTgN3rRyXtMuB%2BA%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}, {"id": "originCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oQFCfQYPpghAEwVfwpQGGW0RGWYMeAqQXQ87fi~tplv-tiktokx-origin.image?dr=9636&x-expires=**********&x-signature=z4%2BZ4TATp96eWDFf4%2B5LZpFyBt8%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}], "webpage_url": "https://www.tiktok.com/@dylan.page/video/7526919590920719638", "webpage_url_basename": "7526919590920719638", "webpage_url_domain": "tiktok.com", "extractor": "TikTok", "extractor_key": "TikTok", "thumbnail": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oQFCfQYPpghAEwVfwpQGGW0RGWYMeAqQXQ87fi~tplv-tiktokx-origin.image?dr=9636&x-expires=**********&x-signature=z4%2BZ4TATp96eWDFf4%2B5LZpFyBt8%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "display_id": "7526919590920719638", "fulltitle": "This a good idea or not??🤔", "duration_string": "33", "upload_date": "20250714", "artist": "<PERSON>", "epoch": 1752623807, "ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_399185-1", "tbr": 399, "quality": 1, "preference": -1, "filesize": 1673286, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oEGGqWWxgwP8Tg0eph0cAEQpAMQRIDfXMffiQA/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=778&bt=389&cs=2&ds=6&ft=4KJMyMzm8Zmo0VsmPI4jVwMbQpWrKsd.&mime_type=video_mp4&qs=11&rc=Mzw2OzxkZjNpOWdlPDo7aEBpM2txdW45cmtpNDMzbzczNUAxLmNgYy4vNTAxNGMwNTRgYSMucy0wMmRzbm5hLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796641&l=202507152356481F2245E1805F8324E4DB&ply_type=2&policy=2&signature=e3f3b2f8490c07aaa884d9c618b82b99&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cp9lUTbaFQ0Eh1AmhH-maF1Bf5DXMXVZvFxYaJa7Yccg%7C1752623807%7C3865684e558183129cbff6b293877aeffefdf38b4f98b573214093a46a72d2a5; Domain=.tiktok.com; Path=/; Expires=1783727807; tt_csrf_token=k9eC2x5c-VwQfmwWulJ2chiFYmektUDUezXw; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"33+dL9xp1hgTnsZVXHep+g==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175808", "format": "bytevc1_540p_399185-1 - 576x1024", "_type": "video", "_version": {"version": "2025.06.30", "release_git_head": "b0187844988e557c7e1e6bb1aabd4c1176768d86", "repository": "yt-dlp/yt-dlp"}}