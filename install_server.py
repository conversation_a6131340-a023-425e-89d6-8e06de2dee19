#!/usr/bin/env python3
"""
Ubuntu Server Installation Script for Social Media Auto Tracker
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(cmd, check=True):
    """Run shell command"""
    print(f"Running: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stderr:
            print(f"Stderr: {e.stderr}")
        return False


def install_dependencies():
    """Install required system packages"""
    print("📦 Installing system dependencies...")
    
    # Update package list
    run_command(['sudo', 'apt', 'update'])
    
    # Install Python and pip
    packages = [
        'python3',
        'python3-pip',
        'python3-venv',
        'git',
        'curl',
        'wget',
        'ffmpeg',  # For video processing
        'gallery-dl'  # For VSCO downloads
    ]
    
    for package in packages:
        print(f"Installing {package}...")
        run_command(['sudo', 'apt', 'install', '-y', package])
    
    # Install Python packages
    print("🐍 Installing Python packages...")
    pip_packages = [
        'instaloader',
        'yt-dlp',
        'requests',
        'beautifulsoup4',
        'pillow'
    ]
    
    for package in pip_packages:
        run_command(['pip3', 'install', '--user', package])


def setup_directories():
    """Setup required directories"""
    print("📁 Setting up directories...")
    
    home = Path.home()
    
    # Create directories
    dirs = [
        home / '.social_tracker',
        home / 'Downloads' / 'SocialMedia',
        home / 'social-tracker'
    ]
    
    for dir_path in dirs:
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"Created: {dir_path}")


def copy_files():
    """Copy tracker files to installation directory"""
    print("📋 Copying tracker files...")
    
    current_dir = Path(__file__).parent
    install_dir = Path.home() / 'social-tracker'
    
    # Core files to copy
    files_to_copy = [
        'auto_tracker.py',
        'tracker_cli.py',
        'config.py',
        'instagram_downloader.py',
        'tiktok_downloader.py',
        'vsco_downloader.py'
    ]
    
    for file_name in files_to_copy:
        src = current_dir / file_name
        dst = install_dir / file_name
        
        if src.exists():
            shutil.copy2(src, dst)
            print(f"Copied: {file_name}")
        else:
            print(f"Warning: {file_name} not found")
    
    # Make scripts executable
    for script in ['auto_tracker.py', 'tracker_cli.py']:
        script_path = install_dir / script
        if script_path.exists():
            script_path.chmod(0o755)


def setup_service():
    """Setup systemd service"""
    print("⚙️ Setting up systemd service...")
    
    current_dir = Path(__file__).parent
    service_file = current_dir / 'social-tracker.service'
    
    if service_file.exists():
        # Update service file with correct paths
        username = os.getenv('USER', 'ubuntu')
        install_dir = Path.home() / 'social-tracker'
        
        service_content = service_file.read_text()
        service_content = service_content.replace('/home/<USER>', str(Path.home()))
        service_content = service_content.replace('User=ubuntu', f'User={username}')
        service_content = service_content.replace('Group=ubuntu', f'Group={username}')
        
        # Write updated service file
        temp_service = Path('/tmp/social-tracker.service')
        temp_service.write_text(service_content)
        
        # Copy to systemd directory
        run_command(['sudo', 'cp', str(temp_service), '/etc/systemd/system/'])
        run_command(['sudo', 'systemctl', 'daemon-reload'])
        
        print("✅ Systemd service installed")
        print("To enable: sudo systemctl enable social-tracker")
        print("To start: sudo systemctl start social-tracker")
    else:
        print("Warning: social-tracker.service not found")


def create_cli_symlink():
    """Create CLI symlink for easy access"""
    print("🔗 Creating CLI symlink...")
    
    install_dir = Path.home() / 'social-tracker'
    cli_script = install_dir / 'tracker_cli.py'
    
    if cli_script.exists():
        # Create symlink in user's local bin
        local_bin = Path.home() / '.local' / 'bin'
        local_bin.mkdir(parents=True, exist_ok=True)
        
        symlink_path = local_bin / 'tracker'
        
        if symlink_path.exists():
            symlink_path.unlink()
        
        symlink_path.symlink_to(cli_script)
        print(f"Created symlink: {symlink_path} -> {cli_script}")
        
        # Add to PATH if not already there
        bashrc = Path.home() / '.bashrc'
        path_line = 'export PATH="$HOME/.local/bin:$PATH"'
        
        if bashrc.exists():
            content = bashrc.read_text()
            if path_line not in content:
                with open(bashrc, 'a') as f:
                    f.write(f'\n# Social Media Tracker CLI\n{path_line}\n')
                print("Added ~/.local/bin to PATH in .bashrc")
        
        print("✅ CLI available as 'tracker' command")
        print("Run 'source ~/.bashrc' or restart shell to use")


def show_usage():
    """Show usage instructions"""
    install_dir = Path.home() / 'social-tracker'
    
    usage = f"""
🎉 Social Media Auto Tracker Installation Complete!

📁 Installation Directory: {install_dir}
📊 Database: ~/.social_tracker/tracker.db
⬇️ Downloads: ~/Downloads/SocialMedia/

🚀 QUICK START:

1. Add users to track:
   tracker add instagram username
   tracker add tiktok username
   tracker add vsco username

2. Start automatic tracking:
   tracker start

3. Or use systemd service:
   sudo systemctl enable social-tracker
   sudo systemctl start social-tracker

📋 AVAILABLE COMMANDS:
   tracker help          # Show all commands
   tracker add <platform> <username>  # Add user
   tracker list           # List tracked users
   tracker check          # Manual check for new content
   tracker stats          # Show statistics
   tracker status         # Check daemon status

🔧 SERVICE MANAGEMENT:
   sudo systemctl start social-tracker    # Start service
   sudo systemctl stop social-tracker     # Stop service
   sudo systemctl status social-tracker   # Check status
   sudo journalctl -u social-tracker -f   # View logs

📱 SUPPORTED PLATFORMS:
   - Instagram (posts, stories, reels)
   - TikTok (videos)
   - VSCO (images)

💡 EXAMPLES:
   tracker add instagram johndoe
   tracker add tiktok @janedoe
   tracker start --interval 3600  # Check every hour
"""
    
    print(usage)


def main():
    """Main installation process"""
    print("🚀 Installing Social Media Auto Tracker for Ubuntu Server")
    print("=" * 60)
    
    # Check if running on Linux
    if sys.platform != 'linux':
        print("❌ This installer is for Ubuntu/Linux servers only")
        sys.exit(1)
    
    # Check if running as root
    if os.geteuid() == 0:
        print("❌ Don't run this installer as root")
        print("💡 Run as regular user (it will use sudo when needed)")
        sys.exit(1)
    
    try:
        # Installation steps
        install_dependencies()
        setup_directories()
        copy_files()
        setup_service()
        create_cli_symlink()
        
        print("\n✅ Installation completed successfully!")
        show_usage()
        
    except KeyboardInterrupt:
        print("\n⏹️ Installation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Installation failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
