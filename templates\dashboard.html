{% extends "base.html" %}

{% block title %}Dashboard - Social Media Tracker{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">📊 Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-primary btn-custom" onclick="checkAllUsers()">
                <i class="fas fa-sync me-1"></i> Check All
            </button>
            <button type="button" class="btn btn-outline-success btn-custom" onclick="toggleDaemon()">
                <i class="fas fa-play me-1"></i> <span id="daemon-toggle-text">Start Daemon</span>
            </button>
        </div>
    </div>
</div>

<!-- Status Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Users</div>
                        <div class="h5 mb-0 font-weight-bold">{{ users|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Downloads</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.total_downloads or 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-download fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Daemon Status</div>
                        <div class="h6 mb-0" id="daemon-status">
                            {% if daemon_status.running %}
                                <span class="badge bg-success">Running</span>
                            {% else %}
                                <span class="badge bg-danger">Stopped</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-robot fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Check Interval</div>
                        <div class="h6 mb-0">{{ daemon_status.interval // 60 }} min</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Platform Overview -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">📱 Platform Overview</h5>
            </div>
            <div class="card-body">
                {% set platforms = {'instagram': 0, 'tiktok': 0, 'vsco': 0} %}
                {% for user in users %}
                    {% set _ = platforms.update({user.platform: platforms[user.platform] + 1}) %}
                {% endfor %}
                
                <div class="row">
                    <div class="col-md-4 text-center">
                        <div class="platform-instagram">
                            <i class="fab fa-instagram fa-3x mb-2"></i>
                            <h4>{{ platforms.instagram }}</h4>
                            <p class="text-muted">Instagram Users</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="platform-tiktok">
                            <i class="fab fa-tiktok fa-3x mb-2"></i>
                            <h4>{{ platforms.tiktok }}</h4>
                            <p class="text-muted">TikTok Users</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="platform-vsco">
                            <i class="fas fa-camera fa-3x mb-2"></i>
                            <h4>{{ platforms.vsco }}</h4>
                            <p class="text-muted">VSCO Users</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">⏰ System Info</h5>
            </div>
            <div class="card-body">
                <p><strong>Last Check:</strong><br>
                <small id="last-check">
                    {% if daemon_status.last_check %}
                        {{ daemon_status.last_check }}
                    {% else %}
                        Never
                    {% endif %}
                </small></p>
                
                <p><strong>Next Check:</strong><br>
                <small>
                    {% if daemon_status.running %}
                        In {{ daemon_status.interval // 60 }} minutes
                    {% else %}
                        Daemon stopped
                    {% endif %}
                </small></p>
                
                <p><strong>Database:</strong><br>
                <small class="text-muted">~/.social_tracker/tracker.db</small></p>
            </div>
        </div>
    </div>
</div>

<!-- Recent Downloads -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">📥 Recent Downloads</h5>
    </div>
    <div class="card-body">
        {% if recent_downloads %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Platform</th>
                            <th>User</th>
                            <th>Content</th>
                            <th>Type</th>
                            <th>Downloaded</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for download in recent_downloads[:10] %}
                        <tr>
                            <td>
                                <span class="badge bg-primary">{{ download.platform }}</span>
                            </td>
                            <td>@{{ download.username }}</td>
                            <td>
                                <small class="text-muted">{{ download.content_id[:20] }}...</small>
                            </td>
                            <td>{{ download.content_type }}</td>
                            <td>
                                <small>{{ download.download_time }}</small>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="text-center">
                <a href="{{ url_for('logs') }}" class="btn btn-outline-primary btn-custom">
                    View All Downloads
                </a>
            </div>
        {% else %}
            <div class="text-center text-muted py-4">
                <i class="fas fa-inbox fa-3x mb-3"></i>
                <p>No downloads yet. Add some users to get started!</p>
                <a href="{{ url_for('users') }}" class="btn btn-primary btn-custom">
                    Add Users
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function checkAllUsers() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Checking...';
    btn.disabled = true;
    
    apiCall('/api/check_all_users', {}, function(data) {
        btn.innerHTML = originalText;
        btn.disabled = false;
        
        if (data.result) {
            showAlert(`Checked ${data.result.total_users} users. Found: ${data.result.total_found}, Downloaded: ${data.result.total_downloaded}`, 'success');
        }
    });
}

function toggleDaemon() {
    const btn = document.getElementById('daemon-toggle-text');
    const isRunning = btn.textContent.includes('Stop');
    const action = isRunning ? 'stop' : 'start';
    
    apiCall('/api/daemon_control', {
        action: action,
        interval: {{ daemon_status.interval }}
    }, function(data) {
        btn.textContent = isRunning ? 'Start Daemon' : 'Stop Daemon';
        
        // Update status badge
        const statusBadge = document.getElementById('daemon-status');
        statusBadge.innerHTML = isRunning ? 
            '<span class="badge bg-danger">Stopped</span>' : 
            '<span class="badge bg-success">Running</span>';
    });
}
</script>
{% endblock %}
