"""
TikTok Downloader - Download videos, user profiles, and content
"""
import os
import logging
import time
import json
import re
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any, Union
import yt_dlp
import requests
from urllib.parse import urlparse
import config

# Simple error classes
class AuthenticationError(Exception):
    pass

class NetworkError(Exception):
    pass

class ContentError(Exception):
    pass

class RateLimitError(Exception):
    pass

# Simple retry decorator
def retry_on_error(max_retries=3, delay=1):
    def decorator(func):
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise
                    time.sleep(delay * (attempt + 1))
            return None
        return wrapper
    return decorator
from file_manager import FileManager


class TikTokDownloader:
    """Main class for downloading TikTok content"""
    
    def __init__(self, output_dir: Optional[Path] = None, cookies_file: Optional[str] = None,
                 username: Optional[str] = None, password: Optional[str] = None):
        """
        Initialize the TikTok downloader

        Args:
            output_dir: Output directory for downloads
            cookies_file: Path to TikTok cookies file for authentication
            username: TikTok username for login
            password: TikTok password for login
        """
        self.output_dir = output_dir or config.DOWNLOADS_DIR
        self.cookies_file = cookies_file
        self.username = username
        self.password = password
        self.file_manager = FileManager()

        # Setup logging
        self._setup_logging()

        # Configure yt-dlp options
        self.ydl_opts = {
            'format': 'best[height<=1080]',  # Best quality up to 1080p
            'outtmpl': str(self.output_dir / 'TikTok' / '%(uploader)s' / 'content' / '%(title).100s_%(id)s.%(ext)s'),
            'writeinfojson': True,  # Save metadata
            'writesubtitles': False,
            'writeautomaticsub': False,
            'ignoreerrors': True,  # Continue on errors
            'no_warnings': False,
            'extractaudio': False,
            'audioformat': 'mp3',
            'embed_subs': False,
            'writesubtitles': False,
            # Support for TikTok images (carousel posts)
            'writethumbnail': True,  # Download thumbnails/images
            'writeall_thumbnails': True,  # Download all image variants
            # Force PNG format for images
            'postprocessors': [{
                'key': 'FFmpegVideoConvertor',
                'preferedformat': 'png',
                'when': 'post_process'
            }]
        }

        # Add authentication if provided
        if self.cookies_file and Path(self.cookies_file).exists():
            self.ydl_opts['cookiefile'] = self.cookies_file
            self.logger.info(f"TikTok downloader initialized with cookies: {self.cookies_file}")
        elif self.username and self.password:
            self.ydl_opts['username'] = self.username
            self.ydl_opts['password'] = self.password
            self.logger.info(f"TikTok downloader initialized with login: {self.username}")
        else:
            self.logger.info("TikTok downloader initialized (public content only)")
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_file = config.LOGS_DIR / f"tiktok_downloader_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=getattr(logging, config.LOG_LEVEL),
            format=config.LOG_FORMAT,
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def _ensure_user_directories(self, username: str) -> Dict[str, Path]:
        """
        Ensure user-specific directories exist
        
        Args:
            username: TikTok username
            
        Returns:
            Dict[str, Path]: Dictionary with paths for content types
        """
        user_dir = self.output_dir / 'TikTok' / username

        directories = {
            'videos': user_dir / 'videos',
            'metadata': user_dir / 'metadata'
        }
        
        # Create directories
        for dir_path in directories.values():
            dir_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"Created/verified directories for user: {username}")
        return directories
    
    def _extract_username_from_url(self, url: str) -> Optional[str]:
        """
        Extract username from TikTok URL
        
        Args:
            url: TikTok URL
            
        Returns:
            Username if found, None otherwise
        """
        patterns = [
            r'tiktok\.com/@([^/\?]+)',
            r'tiktok\.com/([^/\?@]+)',
            r'vm\.tiktok\.com/([^/\?]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename for filesystem
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename
        """
        # Remove invalid characters
        invalid_chars = r'[<>:"/\\|?*]'
        filename = re.sub(invalid_chars, '_', filename)
        
        # Limit length
        if len(filename) > 200:
            name, ext = os.path.splitext(filename)
            filename = name[:200-len(ext)] + ext
        
        return filename
    
    @retry_on_error()
    def download_video(self, url: str, username: Optional[str] = None) -> bool:
        """
        Download a single TikTok video
        
        Args:
            url: TikTok video URL
            username: Optional username for organization
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.info(f"Downloading TikTok video: {url}")
            
            # Extract username from URL if not provided
            if not username:
                username = self._extract_username_from_url(url) or "unknown"
            
            # Ensure directories exist
            user_dirs = self._ensure_user_directories(username)
            
            # Update output template for this download
            opts = self.ydl_opts.copy()
            opts['outtmpl'] = str(user_dirs['videos'] / '%(title).100s_%(id)s.%(ext)s')
            
            with yt_dlp.YoutubeDL(opts) as ydl:
                # Extract info first
                info = ydl.extract_info(url, download=False)
                if not info:
                    raise ContentError("Failed to extract video information")
                
                # Download the video
                ydl.download([url])
                
                self.logger.info(f"Successfully downloaded: {info.get('title', 'Unknown')}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to download video {url}: {str(e)}")
            return False
    
    @retry_on_error()
    def download_user_videos(self, username: str, count: Optional[int] = None) -> bool:
        """
        Download videos from a user's profile
        
        Args:
            username: TikTok username (with or without @)
            count: Maximum number of videos to download
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Clean username
            username = username.lstrip('@')
            self.logger.info(f"Downloading videos from @{username}")
            
            # Ensure directories exist
            user_dirs = self._ensure_user_directories(username)
            
            # Construct user URL
            user_url = f"https://www.tiktok.com/@{username}"
            
            # Update output template
            opts = self.ydl_opts.copy()
            opts['outtmpl'] = str(user_dirs['videos'] / '%(title).100s_%(upload_date)s_%(id)s.%(ext)s')
            
            # Set playlist end if count is specified
            if count:
                opts['playlistend'] = count
            
            with yt_dlp.YoutubeDL(opts) as ydl:
                try:
                    ydl.download([user_url])
                    self.logger.info(f"Successfully downloaded videos from @{username}")
                    return True
                except Exception as e:
                    # Try alternative URL format
                    alt_url = f"https://www.tiktok.com/{username}"
                    self.logger.info(f"Trying alternative URL: {alt_url}")
                    ydl.download([alt_url])
                    self.logger.info(f"Successfully downloaded videos from @{username}")
                    return True
                    
        except Exception as e:
            self.logger.error(f"Failed to download videos from @{username}: {str(e)}")
            return False
    
    def get_user_info(self, username: str) -> Dict[str, Any]:
        """
        Get user profile information
        
        Args:
            username: TikTok username
            
        Returns:
            Dict containing user information
        """
        try:
            username = username.lstrip('@')
            user_url = f"https://www.tiktok.com/@{username}"
            
            opts = {
                'quiet': True,
                'no_warnings': True,
                'extract_flat': True
            }
            
            with yt_dlp.YoutubeDL(opts) as ydl:
                try:
                    info = ydl.extract_info(user_url, download=False)
                    
                    if info:
                        return {
                            'username': username,
                            'user_id': info.get('id', f"tiktok_{username}"),
                            'title': info.get('title', f'@{username}'),
                            'description': info.get('description', ''),
                            'uploader': info.get('uploader', username),
                            'video_count': info.get('playlist_count', 0),
                            'url': user_url
                        }
                except Exception:
                    # Return basic info if extraction fails
                    pass
            
            # Return minimal info if extraction fails
            return {
                'username': username,
                'user_id': f"tiktok_{username}",
                'title': f'@{username}',
                'description': 'Profile information not available',
                'uploader': username,
                'video_count': 0,
                'url': user_url
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get user info for @{username}: {str(e)}")
            return {
                'username': username,
                'user_id': f"tiktok_{username}",
                'title': f'@{username}',
                'description': 'Error loading profile',
                'uploader': username,
                'video_count': 0,
                'url': f"https://www.tiktok.com/@{username}"
            }
    
    def download_from_urls(self, urls: List[str]) -> Dict[str, bool]:
        """
        Download multiple videos from URLs
        
        Args:
            urls: List of TikTok URLs
            
        Returns:
            Dict mapping URLs to success status
        """
        results = {}
        
        for url in urls:
            try:
                results[url] = self.download_video(url)
                # Add delay between downloads
                time.sleep(config.REQUEST_DELAY)
            except Exception as e:
                self.logger.error(f"Failed to download {url}: {str(e)}")
                results[url] = False
        
        return results
    
    def get_download_summary(self, username: str) -> Dict[str, Any]:
        """
        Get download summary for a user
        
        Args:
            username: TikTok username
            
        Returns:
            Dict with download statistics
        """
        try:
            user_dirs = self._ensure_user_directories(username)
            
            # Count downloaded files
            video_count = len([f for f in user_dirs['videos'].iterdir() 
                             if f.is_file() and f.suffix.lower() in ['.mp4', '.mov', '.avi']])
            
            return {
                'username': username,
                'videos_downloaded': video_count,
                'download_path': user_dirs['videos'],
                'total_size': sum(f.stat().st_size for f in user_dirs['videos'].iterdir() if f.is_file())
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get download summary for @{username}: {str(e)}")
            return {
                'username': username,
                'videos_downloaded': 0,
                'download_path': self.output_dir / 'TikTok' / username / 'videos',
                'total_size': 0
            }
    
    def show_download_summary(self, username: str):
        """
        Show download summary with file locations
        
        Args:
            username: TikTok username
        """
        summary = self.get_download_summary(username)
        
        self.logger.info(f"\n📁 Downloads for @{username} saved to: {summary['download_path']}")
        self.logger.info(f"  📹 Videos: {summary['videos_downloaded']} files")
        
        if summary['total_size'] > 0:
            size_mb = summary['total_size'] / (1024 * 1024)
            self.logger.info(f"  💾 Total size: {size_mb:.1f} MB")
