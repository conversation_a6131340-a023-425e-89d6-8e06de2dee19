# 🤖 Social Media Auto Tracker - Ubuntu Server

Automatic tracking and downloading system for Instagram, TikTok, and VSCO content. Designed for Ubuntu servers with no GUI dependencies.

## 🎯 **What It Does**

### **📱 Supported Platforms**
- **Instagram**: Posts, stories, reels with automatic detection
- **TikTok**: User videos with metadata
- **VSCO**: High-resolution images from user galleries

### **🔄 Automatic Features**
- **Background Monitoring**: Continuously checks for new content
- **Incremental Downloads**: Only downloads content you don't already have
- **Smart Scheduling**: Configurable check intervals (15 min - 4 hours)
- **Database Tracking**: SQLite database prevents duplicate downloads
- **Systemd Integration**: Runs as a proper Linux service

## 🚀 **Quick Installation**

### **1. Install on Ubuntu Server**
```bash
# Clone or download the tracker files
git clone <repository> social-tracker
cd social-tracker

# Run installation script
python3 install_server.py
```

### **2. Add Users to Track**
```bash
# Add Instagram users
tracker add instagram johndoe
tracker add instagram janedoe

# Add TikTok users  
tracker add tiktok @tiktoker
tracker add tiktok creator

# Add VSCO users
tracker add vsco photographer
tracker add vsco artist
```

### **3. Start Automatic Tracking**
```bash
# Start daemon manually
tracker start

# Or use systemd service
sudo systemctl enable social-tracker
sudo systemctl start social-tracker
```

## 📋 **CLI Commands**

### **👥 User Management**
```bash
tracker add instagram username     # Add Instagram user
tracker add tiktok username        # Add TikTok user  
tracker add vsco username          # Add VSCO user
tracker remove instagram username  # Remove user
tracker list                       # List all tracked users
tracker list instagram             # List Instagram users only
```

### **🔄 Daemon Control**
```bash
tracker start                      # Start daemon
tracker start --interval 3600     # Start with 1-hour intervals
# Stop with Ctrl+C or kill process
```

### **🔍 Manual Operations**
```bash
tracker check                      # Check all users for new content
tracker check instagram username   # Check specific user
tracker download                   # Same as check (downloads new content)
tracker stats                      # Show tracking statistics
tracker status                     # Check if daemon is running
tracker logs                       # Show recent log entries
```

### **⚙️ Advanced Options**
```bash
# Skip certain content types when adding users
tracker add instagram user --no-stories  # Skip stories
tracker add instagram user --no-reels    # Skip reels
tracker add tiktok user --no-posts       # Skip posts (videos)
```

## 🔧 **Systemd Service Management**

### **Service Commands**
```bash
# Enable service to start on boot
sudo systemctl enable social-tracker

# Start/stop/restart service
sudo systemctl start social-tracker
sudo systemctl stop social-tracker
sudo systemctl restart social-tracker

# Check service status
sudo systemctl status social-tracker

# View service logs
sudo journalctl -u social-tracker -f
sudo journalctl -u social-tracker --since "1 hour ago"
```

### **Service Configuration**
- **Service File**: `/etc/systemd/system/social-tracker.service`
- **Default Interval**: 30 minutes (1800 seconds)
- **User**: Runs as the user who installed it
- **Auto-restart**: Automatically restarts if it crashes

## 📁 **File Locations**

### **Installation Paths**
```
~/social-tracker/           # Main installation directory
├── auto_tracker.py         # Core tracking system
├── tracker_cli.py          # CLI interface
├── config.py               # Configuration
├── instagram_downloader.py # Instagram downloader
├── tiktok_downloader.py    # TikTok downloader
└── vsco_downloader.py      # VSCO downloader
```

### **Data Paths**
```
~/.social_tracker/          # Configuration and data
├── tracker.db             # SQLite database
└── tracker.log            # Application logs

~/Downloads/SocialMedia/   # Downloaded content
├── Instagram/             # Instagram downloads
├── TikTok/               # TikTok downloads
└── VSCO/                 # VSCO downloads
```

## 📊 **Database Schema**

### **Tracked Users**
- Platform, username, display name
- Content type preferences (posts, stories, reels)
- Last check and download timestamps
- Total download counts

### **Downloaded Content**
- Unique content IDs to prevent duplicates
- File paths and metadata
- Download timestamps and post dates
- Platform-specific information

### **Check Sessions**
- Session logs with statistics
- Items found, downloaded, skipped, errors
- Performance and error tracking

## 🔍 **Monitoring & Troubleshooting**

### **Check Status**
```bash
# Quick status check
tracker status

# Detailed statistics
tracker stats

# Recent activity
tracker logs
```

### **Common Issues**

#### **"No tracked users found"**
```bash
# Add some users first
tracker add instagram username
tracker list  # Verify users were added
```

#### **"Daemon not running"**
```bash
# Check if service is enabled
sudo systemctl status social-tracker

# Start manually for debugging
tracker start
```

#### **"Download failures"**
```bash
# Check logs for errors
tracker logs
sudo journalctl -u social-tracker --since "1 hour ago"

# Test specific user
tracker check instagram username
```

#### **"Permission errors"**
```bash
# Ensure directories exist and are writable
mkdir -p ~/.social_tracker ~/Downloads/SocialMedia
chmod 755 ~/.social_tracker ~/Downloads/SocialMedia
```

## ⚡ **Performance Tips**

### **Optimal Settings**
- **Check Interval**: 30-60 minutes for active users
- **User Limit**: 50-100 users per instance
- **Content Types**: Disable unused types (stories, reels) to reduce load

### **Resource Usage**
- **CPU**: Low usage during checks, higher during downloads
- **Memory**: ~50-100MB base, +10MB per active user
- **Storage**: Depends on content volume
- **Network**: Respectful rate limiting built-in

## 🛡️ **Security & Privacy**

### **Data Protection**
- **Local Storage**: All data stays on your server
- **No Cloud Sync**: No external data transmission
- **User Control**: Full control over tracked users and data
- **Secure Database**: SQLite with proper permissions

### **Rate Limiting**
- **Built-in Delays**: Respectful delays between requests
- **Platform Compliance**: Follows platform terms of service
- **Error Handling**: Graceful handling of API limits
- **Retry Logic**: Smart retry on temporary failures

## 💡 **Example Workflows**

### **Personal Archive Server**
```bash
# Set up personal content archiving
tracker add instagram myaccount
tracker add tiktok myaccount
tracker add vsco myaccount

# Start with 1-hour checks
sudo systemctl enable social-tracker
sudo systemctl start social-tracker
```

### **Content Monitoring**
```bash
# Monitor multiple creators
tracker add instagram creator1
tracker add instagram creator2
tracker add tiktok creator3

# Check manually first
tracker check

# Then start automatic monitoring
tracker start --interval 1800
```

### **Batch Management**
```bash
# Add multiple users
for user in user1 user2 user3; do
    tracker add instagram $user
done

# Check all at once
tracker check

# View results
tracker stats
```

This system provides a robust, server-friendly solution for automatically tracking and downloading social media content with minimal maintenance required!
