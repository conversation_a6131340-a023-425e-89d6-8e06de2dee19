#!/usr/bin/env python3
"""
Ubuntu Daemon Service - Simple daemon wrapper for auto_tracker.py
"""
import sys
import os
import signal
import time
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from auto_tracker import AutoTracker


def signal_handler(signum, frame):
    """Handle daemon signals"""
    print(f"Received signal {signum}, shutting down...")
    sys.exit(0)


def main():
    """Main daemon entry point"""
    # Setup signal handlers
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    # Create tracker instance
    tracker = AutoTracker()
    
    # Get interval from environment or use default
    interval = int(os.getenv('TRACKER_INTERVAL', '1800'))  # 30 minutes default
    
    print(f"Starting Social Media Auto Tracker daemon (interval: {interval}s)")
    
    try:
        # Start daemon
        tracker.start_daemon(interval)
    except KeyboardInterrupt:
        print("Daemon stopped by user")
    except Exception as e:
        print(f"Daemon error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
