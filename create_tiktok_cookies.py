#!/usr/bin/env python3
"""
TikTok Cookies Extractor
Creates cookies file for TikTok authentication to access private users
"""
import os
import json
from pathlib import Path

def create_tiktok_cookies():
    """
    Guide user through creating TikTok cookies file
    """
    print("🎵 TikTok Cookies Setup")
    print("=" * 50)
    print()
    print("To download from private TikTok users, you need to extract cookies from your browser.")
    print()
    print("📋 STEPS:")
    print("1. Open TikTok in your browser and log in")
    print("2. Install a browser extension to export cookies:")
    print("   - Chrome: 'Get cookies.txt LOCALLY' extension")
    print("   - Firefox: 'cookies.txt' extension")
    print("3. Export cookies for tiktok.com")
    print("4. Save the cookies file")
    print()
    
    # Create cookies directory
    cookies_dir = Path.home() / ".social_tracker" / "cookies"
    cookies_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"📁 Cookies directory: {cookies_dir}")
    print()
    
    # Ask for cookies file path
    while True:
        cookies_path = input("Enter path to your TikTok cookies file (or 'skip' to continue without): ").strip()
        
        if cookies_path.lower() == 'skip':
            print("⚠️  Skipping TikTok cookies - only public content will be accessible")
            return None
            
        cookies_path = Path(cookies_path).expanduser()
        
        if cookies_path.exists():
            # Copy cookies file to our directory
            target_path = cookies_dir / "tiktok_cookies.txt"
            
            try:
                import shutil
                shutil.copy2(cookies_path, target_path)
                print(f"✅ Cookies copied to: {target_path}")
                
                # Update config
                update_config_with_cookies(str(target_path))
                
                return str(target_path)
                
            except Exception as e:
                print(f"❌ Failed to copy cookies: {e}")
                continue
        else:
            print(f"❌ File not found: {cookies_path}")
            print("Please check the path and try again.")
            continue

def update_config_with_cookies(cookies_path):
    """Update config.py with cookies path"""
    try:
        config_path = Path(__file__).parent / "config.py"
        
        if config_path.exists():
            # Read config
            with open(config_path, 'r') as f:
                content = f.read()
            
            # Update TIKTOK_COOKIES_FILE
            if 'TIKTOK_COOKIES_FILE = None' in content:
                content = content.replace(
                    'TIKTOK_COOKIES_FILE = None',
                    f'TIKTOK_COOKIES_FILE = "{cookies_path}"'
                )
                
                # Write back
                with open(config_path, 'w') as f:
                    f.write(content)
                
                print("✅ Config updated with cookies path")
            else:
                print("⚠️  Please manually update config.py:")
                print(f"   TIKTOK_COOKIES_FILE = \"{cookies_path}\"")
                
    except Exception as e:
        print(f"⚠️  Could not update config automatically: {e}")
        print(f"Please manually update config.py:")
        print(f"   TIKTOK_COOKIES_FILE = \"{cookies_path}\"")

def test_tiktok_cookies():
    """Test TikTok cookies"""
    try:
        from tiktok_downloader import TikTokDownloader
        import config
        
        cookies_file = getattr(config, 'TIKTOK_COOKIES_FILE', None)
        
        if cookies_file and Path(cookies_file).exists():
            downloader = TikTokDownloader(cookies_file=cookies_file)
            print("✅ TikTok downloader initialized with cookies")
            print("🔓 Private TikTok users should now be accessible")
        else:
            downloader = TikTokDownloader()
            print("✅ TikTok downloader initialized without cookies")
            print("🔒 Only public TikTok users will be accessible")
            
    except Exception as e:
        print(f"❌ TikTok downloader test failed: {e}")

if __name__ == "__main__":
    print("🎵 TikTok Authentication Setup")
    print("=" * 50)
    
    cookies_path = create_tiktok_cookies()
    
    if cookies_path:
        print()
        print("🧪 Testing TikTok authentication...")
        test_tiktok_cookies()
    
    print()
    print("🎯 NEXT STEPS:")
    print("1. Test with a private TikTok user:")
    print("   python3 auto_tracker.py add --platform tiktok --username private_user")
    print("   python3 auto_tracker.py check --platform tiktok --username private_user")
    print()
    print("2. If it works, the user's private videos will be downloaded!")
    print()
    print("📝 NOTE: Cookies may expire and need to be refreshed periodically.")
