{"id": "7522533312984878358", "formats": [{"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "download", "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/o8QeIbKjBDOgtALjzLXR8LeAmyILAWejPA0GIk/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2272&bt=1136&cs=0&ds=3&ft=4KJMyMzm8Zmo0RsmPI4jV.TrQpWrKsd.&mime_type=video_mp4&qs=0&rc=NTw0ZjloaWdoaDg2NDo6N0Bpam1laXY5cjNwNDMzbzczNUAzYS9gLjJeNjMxMzJgNDUvYSNoX2xwMmQ0ZGZhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796705&l=20250715235715E658D2630F05372411D2&ply_type=2&policy=2&signature=fc7702897ff05aca38f2f677cb6ce7de&tk=tt_chain_token", "format_note": "watermarked", "preference": -2, "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "dynamic_range": "SDR", "cookies": "ttwid=1%7CDy2ks-85M43EIneWXfAZqD-s2tIY-atz_l7Xg6DUb4o%7C1752623834%7C4186813f007c278a8b9846bbc6e2cad488a1478f465c4d6c1874d6467902fe4d; Domain=.tiktok.com; Path=/; Expires=1783727834; tt_csrf_token=WeGzRDv4-jC_CB-FAz4qSACPDs9fyVljVx74; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"Yq1fF9qvJUnhnUpIbr+KFw==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175835", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522533312984878358"}, "format": "download - unknown (watermarked)"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_1137444-0", "tbr": 1137, "quality": 1, "preference": -1, "filesize": 9981074, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oMTetLAeANrjdGznIS0LBjCFbFIIyQnIAgDKQe/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2220&bt=1110&cs=0&ds=6&ft=4KJMyMzm8Zmo0RsmPI4jV.TrQpWrKsd.&mime_type=video_mp4&qs=0&rc=Mzg5NWg1OmhlNDxpMzM5NUBpam1laXY5cjNwNDMzbzczNUA2YjQ0Ml4wXzAxLmEtNjAwYSNoX2xwMmQ0ZGZhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796705&l=20250715235715E658D2630F05372411D2&ply_type=2&policy=2&signature=2224868ffc52f59c89073d5097fc7e41&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CDy2ks-85M43EIneWXfAZqD-s2tIY-atz_l7Xg6DUb4o%7C1752623834%7C4186813f007c278a8b9846bbc6e2cad488a1478f465c4d6c1874d6467902fe4d; Domain=.tiktok.com; Path=/; Expires=1783727834; tt_csrf_token=WeGzRDv4-jC_CB-FAz4qSACPDs9fyVljVx74; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"Yq1fF9qvJUnhnUpIbr+KFw==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175835", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522533312984878358"}, "format": "h264_540p_1137444-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_1137444-1", "tbr": 1137, "quality": 1, "preference": -1, "filesize": 9981074, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oMTetLAeANrjdGznIS0LBjCFbFIIyQnIAgDKQe/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2220&bt=1110&cs=0&ds=6&ft=4KJMyMzm8Zmo0RsmPI4jV.TrQpWrKsd.&mime_type=video_mp4&qs=0&rc=Mzg5NWg1OmhlNDxpMzM5NUBpam1laXY5cjNwNDMzbzczNUA2YjQ0Ml4wXzAxLmEtNjAwYSNoX2xwMmQ0ZGZhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796705&l=20250715235715E658D2630F05372411D2&ply_type=2&policy=2&signature=2224868ffc52f59c89073d5097fc7e41&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CDy2ks-85M43EIneWXfAZqD-s2tIY-atz_l7Xg6DUb4o%7C1752623834%7C4186813f007c278a8b9846bbc6e2cad488a1478f465c4d6c1874d6467902fe4d; Domain=.tiktok.com; Path=/; Expires=1783727834; tt_csrf_token=WeGzRDv4-jC_CB-FAz4qSACPDs9fyVljVx74; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"Yq1fF9qvJUnhnUpIbr+KFw==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175835", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522533312984878358"}, "format": "h264_540p_1137444-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_485944-0", "tbr": 485, "quality": 1, "preference": -1, "filesize": 4264159, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oczlRWp8oIkEEEF5aBZFhRgEECDQxUA1Agfspf/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=948&bt=474&cs=2&ds=6&ft=4KJMyMzm8Zmo0RsmPI4jV.TrQpWrKsd.&mime_type=video_mp4&qs=11&rc=ZWhlOjpkZzQ7aGYzO2Y1O0Bpam1laXY5cjNwNDMzbzczNUAzMDZhNjIyXmIxMDE0MjMxYSNoX2xwMmQ0ZGZhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796705&l=20250715235715E658D2630F05372411D2&ply_type=2&policy=2&signature=b4dda9053d76e1b3a38b2a2282a3cd2a&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CDy2ks-85M43EIneWXfAZqD-s2tIY-atz_l7Xg6DUb4o%7C1752623834%7C4186813f007c278a8b9846bbc6e2cad488a1478f465c4d6c1874d6467902fe4d; Domain=.tiktok.com; Path=/; Expires=1783727834; tt_csrf_token=WeGzRDv4-jC_CB-FAz4qSACPDs9fyVljVx74; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"Yq1fF9qvJUnhnUpIbr+KFw==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175835", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522533312984878358"}, "format": "bytevc1_540p_485944-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_485944-1", "tbr": 485, "quality": 1, "preference": -1, "filesize": 4264159, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oczlRWp8oIkEEEF5aBZFhRgEECDQxUA1Agfspf/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=948&bt=474&cs=2&ds=6&ft=4KJMyMzm8Zmo0RsmPI4jV.TrQpWrKsd.&mime_type=video_mp4&qs=11&rc=ZWhlOjpkZzQ7aGYzO2Y1O0Bpam1laXY5cjNwNDMzbzczNUAzMDZhNjIyXmIxMDE0MjMxYSNoX2xwMmQ0ZGZhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796705&l=20250715235715E658D2630F05372411D2&ply_type=2&policy=2&signature=b4dda9053d76e1b3a38b2a2282a3cd2a&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CDy2ks-85M43EIneWXfAZqD-s2tIY-atz_l7Xg6DUb4o%7C1752623834%7C4186813f007c278a8b9846bbc6e2cad488a1478f465c4d6c1874d6467902fe4d; Domain=.tiktok.com; Path=/; Expires=1783727834; tt_csrf_token=WeGzRDv4-jC_CB-FAz4qSACPDs9fyVljVx74; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"Yq1fF9qvJUnhnUpIbr+KFw==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175835", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522533312984878358"}, "format": "bytevc1_540p_485944-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_602362-0", "tbr": 602, "quality": 2, "preference": -1, "filesize": 5285733, "width": 720, "height": 1280, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/ogBKAazZ7FlFxEWQIoED5vfpECYhggCfFklA1R/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1176&bt=588&cs=2&ds=3&ft=4KJMyMzm8Zmo0RsmPI4jV.TrQpWrKsd.&mime_type=video_mp4&qs=14&rc=NmdoNjc6aGk7ODc7NDM5Z0Bpam1laXY5cjNwNDMzbzczNUA2YTRfXl81XmMxLWFgYy0wYSNoX2xwMmQ0ZGZhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796705&l=20250715235715E658D2630F05372411D2&ply_type=2&policy=2&signature=6ada865aa62f82ee0c16be846703ffdd&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CDy2ks-85M43EIneWXfAZqD-s2tIY-atz_l7Xg6DUb4o%7C1752623834%7C4186813f007c278a8b9846bbc6e2cad488a1478f465c4d6c1874d6467902fe4d; Domain=.tiktok.com; Path=/; Expires=1783727834; tt_csrf_token=WeGzRDv4-jC_CB-FAz4qSACPDs9fyVljVx74; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"Yq1fF9qvJUnhnUpIbr+KFw==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175835", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522533312984878358"}, "format": "bytevc1_720p_602362-0 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_602362-1", "tbr": 602, "quality": 2, "preference": -1, "filesize": 5285733, "width": 720, "height": 1280, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/ogBKAazZ7FlFxEWQIoED5vfpECYhggCfFklA1R/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1176&bt=588&cs=2&ds=3&ft=4KJMyMzm8Zmo0RsmPI4jV.TrQpWrKsd.&mime_type=video_mp4&qs=14&rc=NmdoNjc6aGk7ODc7NDM5Z0Bpam1laXY5cjNwNDMzbzczNUA2YTRfXl81XmMxLWFgYy0wYSNoX2xwMmQ0ZGZhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796705&l=20250715235715E658D2630F05372411D2&ply_type=2&policy=2&signature=6ada865aa62f82ee0c16be846703ffdd&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CDy2ks-85M43EIneWXfAZqD-s2tIY-atz_l7Xg6DUb4o%7C1752623834%7C4186813f007c278a8b9846bbc6e2cad488a1478f465c4d6c1874d6467902fe4d; Domain=.tiktok.com; Path=/; Expires=1783727834; tt_csrf_token=WeGzRDv4-jC_CB-FAz4qSACPDs9fyVljVx74; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"Yq1fF9qvJUnhnUpIbr+KFw==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175835", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522533312984878358"}, "format": "bytevc1_720p_602362-1 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_1159801-0", "tbr": 1159, "quality": 3, "preference": -1, "filesize": 10172910, "width": 1080, "height": 1920, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oQzlAxgEsfRCQEcI6BFoAa5DgpkZJFgzfWZE15/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2264&bt=1132&cs=2&ds=4&ft=4KJMyMzm8Zmo0RsmPI4jV.TrQpWrKsd.&mime_type=video_mp4&qs=15&rc=ZWg5NTc2ZzU7PDc6ZjUzZEBpam1laXY5cjNwNDMzbzczNUAxYjItYi00XmIxYzNeNmEuYSNoX2xwMmQ0ZGZhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796705&l=20250715235715E658D2630F05372411D2&ply_type=2&policy=2&signature=eefb139b73b3b38958c65e247585a74f&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CDy2ks-85M43EIneWXfAZqD-s2tIY-atz_l7Xg6DUb4o%7C1752623834%7C4186813f007c278a8b9846bbc6e2cad488a1478f465c4d6c1874d6467902fe4d; Domain=.tiktok.com; Path=/; Expires=1783727834; tt_csrf_token=WeGzRDv4-jC_CB-FAz4qSACPDs9fyVljVx74; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"Yq1fF9qvJUnhnUpIbr+KFw==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175835", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522533312984878358"}, "format": "bytevc1_1080p_1159801-0 - 1080x1920"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_1159801-1", "tbr": 1159, "quality": 3, "preference": -1, "filesize": 10172910, "width": 1080, "height": 1920, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oQzlAxgEsfRCQEcI6BFoAa5DgpkZJFgzfWZE15/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2264&bt=1132&cs=2&ds=4&ft=4KJMyMzm8Zmo0RsmPI4jV.TrQpWrKsd.&mime_type=video_mp4&qs=15&rc=ZWg5NTc2ZzU7PDc6ZjUzZEBpam1laXY5cjNwNDMzbzczNUAxYjItYi00XmIxYzNeNmEuYSNoX2xwMmQ0ZGZhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796705&l=20250715235715E658D2630F05372411D2&ply_type=2&policy=2&signature=eefb139b73b3b38958c65e247585a74f&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CDy2ks-85M43EIneWXfAZqD-s2tIY-atz_l7Xg6DUb4o%7C1752623834%7C4186813f007c278a8b9846bbc6e2cad488a1478f465c4d6c1874d6467902fe4d; Domain=.tiktok.com; Path=/; Expires=1783727834; tt_csrf_token=WeGzRDv4-jC_CB-FAz4qSACPDs9fyVljVx74; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"Yq1fF9qvJUnhnUpIbr+KFw==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175835", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522533312984878358"}, "format": "bytevc1_1080p_1159801-1 - 1080x1920"}], "subtitles": {}, "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522533312984878358"}, "channel": "<PERSON>", "channel_id": "MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader": "dylan.page", "uploader_id": "6760368158794155013", "channel_url": "https://www.tiktok.com/@MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader_url": "https://www.tiktok.com/@dylan.page", "track": "original sound", "artists": ["<PERSON>"], "duration": 70, "title": "Is Alligator Alcatraz a W or L? 🤔", "description": "Is Alligator Alcatraz a W or L? 🤔", "timestamp": 1751476277, "view_count": 4900000, "like_count": 436100, "repost_count": 18600, "comment_count": 6759, "thumbnails": [{"id": "dynamicCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oA4VHalpEBNaBi4BAY8EJABiAaaGdzIoRDPAB~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=orQlKOmg9PUWHy5B19HZPs8DULs%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -2}, {"id": "cover", "url": "https://p19-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oA4VHalpEBNaBi4BAY8EJABiAaaGdzIoRDPAB~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=v4lW1xQfjdJCz2A5anYxAqEeVoo%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}, {"id": "originCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oAa5zFxEEI1IgrZ1WlfgpjAARsfXoDkCFBTEBS~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=tx47A0KubH2rfOTUXverLxVbNyc%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}], "webpage_url": "https://www.tiktok.com/@dylan.page/video/7522533312984878358", "webpage_url_basename": "7522533312984878358", "webpage_url_domain": "tiktok.com", "extractor": "TikTok", "extractor_key": "TikTok", "thumbnail": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oAa5zFxEEI1IgrZ1WlfgpjAARsfXoDkCFBTEBS~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=tx47A0KubH2rfOTUXverLxVbNyc%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "display_id": "7522533312984878358", "fulltitle": "Is Alligator Alcatraz a W or L? 🤔", "duration_string": "1:10", "upload_date": "20250702", "artist": "<PERSON>", "epoch": 1752623834, "ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_485944-1", "tbr": 485, "quality": 1, "preference": -1, "filesize": 4264159, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oczlRWp8oIkEEEF5aBZFhRgEECDQxUA1Agfspf/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=948&bt=474&cs=2&ds=6&ft=4KJMyMzm8Zmo0RsmPI4jV.TrQpWrKsd.&mime_type=video_mp4&qs=11&rc=ZWhlOjpkZzQ7aGYzO2Y1O0Bpam1laXY5cjNwNDMzbzczNUAzMDZhNjIyXmIxMDE0MjMxYSNoX2xwMmQ0ZGZhLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796705&l=20250715235715E658D2630F05372411D2&ply_type=2&policy=2&signature=b4dda9053d76e1b3a38b2a2282a3cd2a&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CDy2ks-85M43EIneWXfAZqD-s2tIY-atz_l7Xg6DUb4o%7C1752623834%7C4186813f007c278a8b9846bbc6e2cad488a1478f465c4d6c1874d6467902fe4d; Domain=.tiktok.com; Path=/; Expires=1783727834; tt_csrf_token=WeGzRDv4-jC_CB-FAz4qSACPDs9fyVljVx74; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"Yq1fF9qvJUnhnUpIbr+KFw==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175835", "format": "bytevc1_540p_485944-1 - 576x1024", "_type": "video", "_version": {"version": "2025.06.30", "release_git_head": "b0187844988e557c7e1e6bb1aabd4c1176768d86", "repository": "yt-dlp/yt-dlp"}}