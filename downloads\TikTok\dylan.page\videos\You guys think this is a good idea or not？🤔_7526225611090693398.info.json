{"id": "7526225611090693398", "formats": [{"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "download", "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oMJyIUTS0IyPCxC2hAOi9RMiE2BQhXAZGwfmnV/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2432&bt=1216&cs=0&ds=3&ft=4KJMyMzm8Zmo0-smPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=0&rc=ZzVnNzxpNTw1Zzk7ZmQ1ZUBpajxzb3A5cjY7NDMzbzczNUBgYjUvX2EzXjMxNS8tXl8vYSM2ZmJkMmRjYG1hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796717&l=20250715235651B6DFCAD3C316A621BD77&ply_type=2&policy=2&signature=e6e60739f18cda9b36c5a293f181e213&tk=tt_chain_token", "format_note": "watermarked", "preference": -2, "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "dynamic_range": "SDR", "cookies": "ttwid=1%7CgefJtAtJ3SNggJdTD5wGZWGYFfstBl_zlxgkqPBV7VU%7C1752623810%7C6d6eb1f3505087fcccc3f2b0c4ae41fd219d1d3bc1dd6fc4875f28ab4bd03af0; Domain=.tiktok.com; Path=/; Expires=1783727810; tt_csrf_token=wiTPk2CJ-r6EQry1pZqwwWNgqB-w2t_6x9t0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"0exQzpjmj8VrkVZE+Xeaug==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175811", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526225611090693398"}, "format": "download - unknown (watermarked)"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_1225332-0", "tbr": 1225, "quality": 1, "preference": -1, "filesize": 16271495, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oUhU0CE2MQBZw2AGI5JCAGVyTyioIOJiAPRhfh/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2392&bt=1196&cs=0&ds=6&ft=4KJMyMzm8Zmo0-smPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=0&rc=aGg6NDM1ZTQ7Mzo6NWRoNkBpajxzb3A5cjY7NDMzbzczNUAzLTAyNjAwNTUxMzQyYjZfYSM2ZmJkMmRjYG1hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796717&l=20250715235651B6DFCAD3C316A621BD77&ply_type=2&policy=2&signature=09a91c46a0eb3cf47732fa468ceb752d&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CgefJtAtJ3SNggJdTD5wGZWGYFfstBl_zlxgkqPBV7VU%7C1752623810%7C6d6eb1f3505087fcccc3f2b0c4ae41fd219d1d3bc1dd6fc4875f28ab4bd03af0; Domain=.tiktok.com; Path=/; Expires=1783727810; tt_csrf_token=wiTPk2CJ-r6EQry1pZqwwWNgqB-w2t_6x9t0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"0exQzpjmj8VrkVZE+Xeaug==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175811", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526225611090693398"}, "format": "h264_540p_1225332-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_1225332-1", "tbr": 1225, "quality": 1, "preference": -1, "filesize": 16271495, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oUhU0CE2MQBZw2AGI5JCAGVyTyioIOJiAPRhfh/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2392&bt=1196&cs=0&ds=6&ft=4KJMyMzm8Zmo0-smPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=0&rc=aGg6NDM1ZTQ7Mzo6NWRoNkBpajxzb3A5cjY7NDMzbzczNUAzLTAyNjAwNTUxMzQyYjZfYSM2ZmJkMmRjYG1hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796717&l=20250715235651B6DFCAD3C316A621BD77&ply_type=2&policy=2&signature=09a91c46a0eb3cf47732fa468ceb752d&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CgefJtAtJ3SNggJdTD5wGZWGYFfstBl_zlxgkqPBV7VU%7C1752623810%7C6d6eb1f3505087fcccc3f2b0c4ae41fd219d1d3bc1dd6fc4875f28ab4bd03af0; Domain=.tiktok.com; Path=/; Expires=1783727810; tt_csrf_token=wiTPk2CJ-r6EQry1pZqwwWNgqB-w2t_6x9t0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"0exQzpjmj8VrkVZE+Xeaug==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175811", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526225611090693398"}, "format": "h264_540p_1225332-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_491900-0", "tbr": 491, "quality": 1, "preference": -1, "filesize": 6532072, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oUV0OQBZICAiGp2IfCAwEkOi2UyG2TJMT1hIyi/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=960&bt=480&cs=2&ds=6&ft=4KJMyMzm8Zmo0-smPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=11&rc=OzU7NjhnOmlmOzU5NjVpZ0Bpajxzb3A5cjY7NDMzbzczNUAuNjNhMzY1NjExLi5eLWBiYSM2ZmJkMmRjYG1hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796717&l=20250715235651B6DFCAD3C316A621BD77&ply_type=2&policy=2&signature=bd003a1fbebf084cef65d7c0b10e1b50&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CgefJtAtJ3SNggJdTD5wGZWGYFfstBl_zlxgkqPBV7VU%7C1752623810%7C6d6eb1f3505087fcccc3f2b0c4ae41fd219d1d3bc1dd6fc4875f28ab4bd03af0; Domain=.tiktok.com; Path=/; Expires=1783727810; tt_csrf_token=wiTPk2CJ-r6EQry1pZqwwWNgqB-w2t_6x9t0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"0exQzpjmj8VrkVZE+Xeaug==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175811", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526225611090693398"}, "format": "bytevc1_540p_491900-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_491900-1", "tbr": 491, "quality": 1, "preference": -1, "filesize": 6532072, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oUV0OQBZICAiGp2IfCAwEkOi2UyG2TJMT1hIyi/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=960&bt=480&cs=2&ds=6&ft=4KJMyMzm8Zmo0-smPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=11&rc=OzU7NjhnOmlmOzU5NjVpZ0Bpajxzb3A5cjY7NDMzbzczNUAuNjNhMzY1NjExLi5eLWBiYSM2ZmJkMmRjYG1hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796717&l=20250715235651B6DFCAD3C316A621BD77&ply_type=2&policy=2&signature=bd003a1fbebf084cef65d7c0b10e1b50&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CgefJtAtJ3SNggJdTD5wGZWGYFfstBl_zlxgkqPBV7VU%7C1752623810%7C6d6eb1f3505087fcccc3f2b0c4ae41fd219d1d3bc1dd6fc4875f28ab4bd03af0; Domain=.tiktok.com; Path=/; Expires=1783727810; tt_csrf_token=wiTPk2CJ-r6EQry1pZqwwWNgqB-w2t_6x9t0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"0exQzpjmj8VrkVZE+Xeaug==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175811", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526225611090693398"}, "format": "bytevc1_540p_491900-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_607846-0", "tbr": 607, "quality": 2, "preference": -1, "filesize": 8071751, "width": 720, "height": 1280, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oEi2JihCiEpIwaOZUyfMTHGAIsVB0By2CQMYAk/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1186&bt=593&cs=2&ds=3&ft=4KJMyMzm8Zmo0-smPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=14&rc=OzU1OGc2OGU8PDdkaDpkZ0Bpajxzb3A5cjY7NDMzbzczNUBiLi80YDUyNTQxXmJeLTFiYSM2ZmJkMmRjYG1hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796717&l=20250715235651B6DFCAD3C316A621BD77&ply_type=2&policy=2&signature=984b05cb35c3c826002a1576e0a530eb&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CgefJtAtJ3SNggJdTD5wGZWGYFfstBl_zlxgkqPBV7VU%7C1752623810%7C6d6eb1f3505087fcccc3f2b0c4ae41fd219d1d3bc1dd6fc4875f28ab4bd03af0; Domain=.tiktok.com; Path=/; Expires=1783727810; tt_csrf_token=wiTPk2CJ-r6EQry1pZqwwWNgqB-w2t_6x9t0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"0exQzpjmj8VrkVZE+Xeaug==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175811", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526225611090693398"}, "format": "bytevc1_720p_607846-0 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_607846-1", "tbr": 607, "quality": 2, "preference": -1, "filesize": 8071751, "width": 720, "height": 1280, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oEi2JihCiEpIwaOZUyfMTHGAIsVB0By2CQMYAk/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1186&bt=593&cs=2&ds=3&ft=4KJMyMzm8Zmo0-smPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=14&rc=OzU1OGc2OGU8PDdkaDpkZ0Bpajxzb3A5cjY7NDMzbzczNUBiLi80YDUyNTQxXmJeLTFiYSM2ZmJkMmRjYG1hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796717&l=20250715235651B6DFCAD3C316A621BD77&ply_type=2&policy=2&signature=984b05cb35c3c826002a1576e0a530eb&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CgefJtAtJ3SNggJdTD5wGZWGYFfstBl_zlxgkqPBV7VU%7C1752623810%7C6d6eb1f3505087fcccc3f2b0c4ae41fd219d1d3bc1dd6fc4875f28ab4bd03af0; Domain=.tiktok.com; Path=/; Expires=1783727810; tt_csrf_token=wiTPk2CJ-r6EQry1pZqwwWNgqB-w2t_6x9t0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"0exQzpjmj8VrkVZE+Xeaug==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175811", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526225611090693398"}, "format": "bytevc1_720p_607846-1 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_1014072-0", "tbr": 1014, "quality": 3, "preference": -1, "filesize": 13462695, "width": 1080, "height": 1920, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oEU0AyFMiGWiZtiA5wbIOTh1IMV2BCQ2Cf9EJy/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1980&bt=990&cs=2&ds=4&ft=4KJMyMzm8Zmo0-smPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=15&rc=aTUzZDo2OzY7aGk6NTlmOkBpajxzb3A5cjY7NDMzbzczNUAvM2BeYDMvNV8xNmM0MDRiYSM2ZmJkMmRjYG1hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796717&l=20250715235651B6DFCAD3C316A621BD77&ply_type=2&policy=2&signature=701b669de6b70e9734e33e1a7d066011&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CgefJtAtJ3SNggJdTD5wGZWGYFfstBl_zlxgkqPBV7VU%7C1752623810%7C6d6eb1f3505087fcccc3f2b0c4ae41fd219d1d3bc1dd6fc4875f28ab4bd03af0; Domain=.tiktok.com; Path=/; Expires=1783727810; tt_csrf_token=wiTPk2CJ-r6EQry1pZqwwWNgqB-w2t_6x9t0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"0exQzpjmj8VrkVZE+Xeaug==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175811", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526225611090693398"}, "format": "bytevc1_1080p_1014072-0 - 1080x1920"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_1014072-1", "tbr": 1014, "quality": 3, "preference": -1, "filesize": 13462695, "width": 1080, "height": 1920, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oEU0AyFMiGWiZtiA5wbIOTh1IMV2BCQ2Cf9EJy/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1980&bt=990&cs=2&ds=4&ft=4KJMyMzm8Zmo0-smPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=15&rc=aTUzZDo2OzY7aGk6NTlmOkBpajxzb3A5cjY7NDMzbzczNUAvM2BeYDMvNV8xNmM0MDRiYSM2ZmJkMmRjYG1hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796717&l=20250715235651B6DFCAD3C316A621BD77&ply_type=2&policy=2&signature=701b669de6b70e9734e33e1a7d066011&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CgefJtAtJ3SNggJdTD5wGZWGYFfstBl_zlxgkqPBV7VU%7C1752623810%7C6d6eb1f3505087fcccc3f2b0c4ae41fd219d1d3bc1dd6fc4875f28ab4bd03af0; Domain=.tiktok.com; Path=/; Expires=1783727810; tt_csrf_token=wiTPk2CJ-r6EQry1pZqwwWNgqB-w2t_6x9t0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"0exQzpjmj8VrkVZE+Xeaug==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175811", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526225611090693398"}, "format": "bytevc1_1080p_1014072-1 - 1080x1920"}], "subtitles": {}, "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7526225611090693398"}, "channel": "<PERSON>", "channel_id": "MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader": "dylan.page", "uploader_id": "6760368158794155013", "channel_url": "https://www.tiktok.com/@MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader_url": "https://www.tiktok.com/@dylan.page", "track": "original sound", "artists": ["<PERSON>"], "duration": 106, "title": "You guys think this is a good idea or not?🤔", "description": "You guys think this is a good idea or not?🤔", "timestamp": 1752335958, "view_count": 6100000, "like_count": 851000, "repost_count": 38400, "comment_count": 7013, "thumbnails": [{"id": "dynamicCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oEEYaVEgkWAFiEohBXRKgcfeAZDvFCAIzAloYy~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=effVRcQ2ZOhoBIZfk%2BmBc8jcvj8%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -2}, {"id": "cover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oEEYaVEgkWAFiEohBXRKgcfeAZDvFCAIzAloYy~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=effVRcQ2ZOhoBIZfk%2BmBc8jcvj8%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}, {"id": "originCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oMAwAhJOoilahCUBI0iB2lfTI2gzwM2VCyyGZI~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=Jp0tfrEeBXxlkUyjE%2BZLIdE3E9g%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}], "webpage_url": "https://www.tiktok.com/@dylan.page/video/7526225611090693398", "webpage_url_basename": "7526225611090693398", "webpage_url_domain": "tiktok.com", "extractor": "TikTok", "extractor_key": "TikTok", "thumbnail": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oMAwAhJOoilahCUBI0iB2lfTI2gzwM2VCyyGZI~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=Jp0tfrEeBXxlkUyjE%2BZLIdE3E9g%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "display_id": "7526225611090693398", "fulltitle": "You guys think this is a good idea or not?🤔", "duration_string": "1:46", "upload_date": "20250712", "artist": "<PERSON>", "epoch": 1752623810, "ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_491900-1", "tbr": 491, "quality": 1, "preference": -1, "filesize": 6532072, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oUV0OQBZICAiGp2IfCAwEkOi2UyG2TJMT1hIyi/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=960&bt=480&cs=2&ds=6&ft=4KJMyMzm8Zmo0-smPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=11&rc=OzU7NjhnOmlmOzU5NjVpZ0Bpajxzb3A5cjY7NDMzbzczNUAuNjNhMzY1NjExLi5eLWBiYSM2ZmJkMmRjYG1hLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796717&l=20250715235651B6DFCAD3C316A621BD77&ply_type=2&policy=2&signature=bd003a1fbebf084cef65d7c0b10e1b50&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CgefJtAtJ3SNggJdTD5wGZWGYFfstBl_zlxgkqPBV7VU%7C1752623810%7C6d6eb1f3505087fcccc3f2b0c4ae41fd219d1d3bc1dd6fc4875f28ab4bd03af0; Domain=.tiktok.com; Path=/; Expires=1783727810; tt_csrf_token=wiTPk2CJ-r6EQry1pZqwwWNgqB-w2t_6x9t0; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"0exQzpjmj8VrkVZE+Xeaug==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175811", "format": "bytevc1_540p_491900-1 - 576x1024", "_type": "video", "_version": {"version": "2025.06.30", "release_git_head": "b0187844988e557c7e1e6bb1aabd4c1176768d86", "repository": "yt-dlp/yt-dlp"}}