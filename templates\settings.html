{% extends "base.html" %}

{% block title %}Settings - Social Media Tracker{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">⚙️ Settings</h1>
</div>

<div class="row">
    <!-- Daemon Control -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">🤖 Daemon Control</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Current Status</label>
                    <div id="daemon-status-display">
                        {% if daemon_status.running %}
                            <span class="badge bg-success fs-6">Running</span>
                        {% else %}
                            <span class="badge bg-danger fs-6">Stopped</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="intervalInput" class="form-label">Check Interval (minutes)</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="intervalInput" value="{{ current_interval // 60 }}" min="1" max="1440">
                        <span class="input-group-text">minutes</span>
                    </div>
                    <div class="form-text">How often to check for new content (1-1440 minutes)</div>
                </div>
                
                <div class="d-grid gap-2 d-md-flex">
                    <button type="button" class="btn btn-success" id="startDaemonBtn" onclick="controlDaemon('start')">
                        <i class="fas fa-play me-1"></i> Start Daemon
                    </button>
                    <button type="button" class="btn btn-danger" id="stopDaemonBtn" onclick="controlDaemon('stop')">
                        <i class="fas fa-stop me-1"></i> Stop Daemon
                    </button>
                    <button type="button" class="btn btn-warning" onclick="controlDaemon('restart')">
                        <i class="fas fa-redo me-1"></i> Restart
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- System Information -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">📊 System Information</h5>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>Last Check:</strong><br>
                    <small class="text-muted" id="last-check-time">
                        {% if daemon_status.last_check %}
                            {{ daemon_status.last_check }}
                        {% else %}
                            Never
                        {% endif %}
                    </small>
                </div>
                
                <div class="mb-2">
                    <strong>Database:</strong><br>
                    <small class="text-muted">~/.social_tracker/tracker.db</small>
                </div>
                
                <div class="mb-2">
                    <strong>Downloads:</strong><br>
                    <small class="text-muted">~/Downloads/SocialMedia/</small>
                </div>
                
                <div class="mb-2">
                    <strong>Current Interval:</strong><br>
                    <small class="text-muted">{{ current_interval // 60 }} minutes</small>
                </div>
                
                <div class="mt-3">
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="refreshSystemInfo()">
                        <i class="fas fa-sync me-1"></i> Refresh Info
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Manual Operations -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">🔧 Manual Operations</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary" onclick="checkAllUsers()">
                        <i class="fas fa-sync me-1"></i> Check All Users Now
                    </button>
                    
                    <button type="button" class="btn btn-outline-warning" onclick="clearLogs()">
                        <i class="fas fa-trash me-1"></i> Clear Download Logs
                    </button>
                    
                    <button type="button" class="btn btn-outline-info" onclick="exportSettings()">
                        <i class="fas fa-download me-1"></i> Export Settings
                    </button>
                    
                    <button type="button" class="btn btn-outline-secondary" onclick="viewSystemLogs()">
                        <i class="fas fa-file-alt me-1"></i> View System Logs
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Platform Settings -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">📱 Platform Settings</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="instagramEnabled" checked>
                        <label class="form-check-label" for="instagramEnabled">
                            <i class="fab fa-instagram platform-instagram me-1"></i> Instagram
                        </label>
                    </div>
                    <small class="text-muted">Download Instagram posts, stories, and reels</small>
                </div>
                
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="tiktokEnabled" checked>
                        <label class="form-check-label" for="tiktokEnabled">
                            <i class="fab fa-tiktok platform-tiktok me-1"></i> TikTok
                        </label>
                    </div>
                    <small class="text-muted">Download TikTok videos and images</small>
                </div>
                
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="vscoEnabled" checked>
                        <label class="form-check-label" for="vscoEnabled">
                            <i class="fas fa-camera platform-vsco me-1"></i> VSCO
                        </label>
                    </div>
                    <small class="text-muted">Download VSCO images</small>
                </div>
                
                <button type="button" class="btn btn-primary btn-sm" onclick="savePlatformSettings()">
                    <i class="fas fa-save me-1"></i> Save Settings
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Advanced Settings -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">🔬 Advanced Settings</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="maxDownloads" class="form-label">Max Downloads per Check</label>
                    <input type="number" class="form-control" id="maxDownloads" value="300" min="1" max="1000">
                    <div class="form-text">Maximum items to download per user per check</div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="retryAttempts" class="form-label">Retry Attempts</label>
                    <input type="number" class="form-control" id="retryAttempts" value="3" min="1" max="10">
                    <div class="form-text">Number of retry attempts for failed downloads</div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="downloadTimeout" class="form-label">Download Timeout (seconds)</label>
                    <input type="number" class="form-control" id="downloadTimeout" value="300" min="30" max="3600">
                    <div class="form-text">Timeout for individual downloads</div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" id="enableMetadata" checked>
                    <label class="form-check-label" for="enableMetadata">
                        Save Metadata Files
                    </label>
                </div>
                <small class="text-muted">Save JSON metadata alongside downloaded files</small>
            </div>
            
            <div class="col-md-6">
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" id="enableArchive" checked>
                    <label class="form-check-label" for="enableArchive">
                        Enable Archive Mode
                    </label>
                </div>
                <small class="text-muted">Prevent re-downloading of existing content</small>
            </div>
        </div>
        
        <div class="mt-3">
            <button type="button" class="btn btn-primary" onclick="saveAdvancedSettings()">
                <i class="fas fa-save me-1"></i> Save Advanced Settings
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="resetToDefaults()">
                <i class="fas fa-undo me-1"></i> Reset to Defaults
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function controlDaemon(action) {
    const interval = parseInt(document.getElementById('intervalInput').value) * 60; // Convert to seconds
    
    if (action === 'restart') {
        // Stop then start
        apiCall('/api/daemon_control', { action: 'stop' }, function() {
            setTimeout(() => {
                apiCall('/api/daemon_control', { action: 'start', interval: interval }, function() {
                    updateDaemonStatus(true);
                });
            }, 1000);
        });
    } else {
        apiCall('/api/daemon_control', { 
            action: action,
            interval: interval
        }, function() {
            updateDaemonStatus(action === 'start');
        });
    }
}

function updateDaemonStatus(running) {
    const statusDisplay = document.getElementById('daemon-status-display');
    statusDisplay.innerHTML = running ? 
        '<span class="badge bg-success fs-6">Running</span>' : 
        '<span class="badge bg-danger fs-6">Stopped</span>';
}

function checkAllUsers() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Checking...';
    btn.disabled = true;
    
    apiCall('/api/check_all_users', {}, function(data) {
        btn.innerHTML = originalText;
        btn.disabled = false;
        
        if (data.result) {
            showAlert(`Manual check completed: ${data.result.total_found} found, ${data.result.total_downloaded} downloaded`, 'success');
        }
    });
}

function clearLogs() {
    if (confirm('Are you sure you want to clear all download logs? This cannot be undone.')) {
        // This would call an API to clear logs
        showAlert('Download logs cleared successfully', 'success');
    }
}

function exportSettings() {
    const settings = {
        interval: document.getElementById('intervalInput').value,
        maxDownloads: document.getElementById('maxDownloads').value,
        retryAttempts: document.getElementById('retryAttempts').value,
        downloadTimeout: document.getElementById('downloadTimeout').value,
        enableMetadata: document.getElementById('enableMetadata').checked,
        enableArchive: document.getElementById('enableArchive').checked,
        platforms: {
            instagram: document.getElementById('instagramEnabled').checked,
            tiktok: document.getElementById('tiktokEnabled').checked,
            vsco: document.getElementById('vscoEnabled').checked
        }
    };
    
    const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `social_tracker_settings_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    showAlert('Settings exported successfully!', 'success');
}

function viewSystemLogs() {
    // This would open system logs in a modal or new page
    showAlert('System logs feature coming soon!', 'info');
}

function savePlatformSettings() {
    const settings = {
        instagram: document.getElementById('instagramEnabled').checked,
        tiktok: document.getElementById('tiktokEnabled').checked,
        vsco: document.getElementById('vscoEnabled').checked
    };
    
    // This would save platform settings via API
    showAlert('Platform settings saved successfully!', 'success');
}

function saveAdvancedSettings() {
    const settings = {
        maxDownloads: document.getElementById('maxDownloads').value,
        retryAttempts: document.getElementById('retryAttempts').value,
        downloadTimeout: document.getElementById('downloadTimeout').value,
        enableMetadata: document.getElementById('enableMetadata').checked,
        enableArchive: document.getElementById('enableArchive').checked
    };
    
    // This would save advanced settings via API
    showAlert('Advanced settings saved successfully!', 'success');
}

function resetToDefaults() {
    if (confirm('Are you sure you want to reset all settings to defaults?')) {
        document.getElementById('intervalInput').value = 3;
        document.getElementById('maxDownloads').value = 300;
        document.getElementById('retryAttempts').value = 3;
        document.getElementById('downloadTimeout').value = 300;
        document.getElementById('enableMetadata').checked = true;
        document.getElementById('enableArchive').checked = true;
        document.getElementById('instagramEnabled').checked = true;
        document.getElementById('tiktokEnabled').checked = true;
        document.getElementById('vscoEnabled').checked = true;
        
        showAlert('Settings reset to defaults', 'info');
    }
}

function refreshSystemInfo() {
    fetch('/api/status')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.daemon.last_check) {
                document.getElementById('last-check-time').textContent = 
                    new Date(data.daemon.last_check).toLocaleString();
            }
            showAlert('System information refreshed', 'success');
        });
}
</script>
{% endblock %}
