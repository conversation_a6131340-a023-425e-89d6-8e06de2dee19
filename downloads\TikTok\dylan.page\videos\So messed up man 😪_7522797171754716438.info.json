{"id": "7522797171754716438", "formats": [{"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "download", "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/ocfzDfgiIE0IIIsY8gQAFD1MQgjKA9FuLLeK1k/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1696&bt=848&cs=0&ds=3&ft=4KJMyMzm8Zmo0wsmPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=0&rc=PDc8ZjszZDU1Zzo8NjRmZkBpamZkOWo5cnI4NDMzbzczNUBfYjEzLzA1NS0xXy4tLjAwYSNfMmtjMmRrY2dhLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796683&l=20250715235711846F1FF378B2EF24DC22&ply_type=2&policy=2&signature=31e6b0858b7b9ac3a20ebda3b0dc19e5&tk=tt_chain_token", "format_note": "watermarked", "preference": -2, "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "dynamic_range": "SDR", "cookies": "ttwid=1%7CrjofomLFNq-U6Gi3fDG4S0DQEKQH5EhEC5Aobx6uoCI%7C1752623831%7Cf89f9b2f0650f209d9509d81e310fb345cced4a68d8c98c502afd139395758cb; Domain=.tiktok.com; Path=/; Expires=1783727831; tt_csrf_token=URSFWvE6-yDWXk4ZB6J39v6XBQv_3fSsfJa4; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"KduUjn/T5YhYPcky+bLzMg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175831", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522797171754716438"}, "format": "download - unknown (watermarked)"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_832743-0", "tbr": 832, "quality": 1, "preference": -1, "filesize": 5508286, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/ogImEQLQjW6zAxDQeFNqfslaXfgIiEFAIiKEDA/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1626&bt=813&cs=0&ds=6&ft=4KJMyMzm8Zmo0wsmPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=0&rc=aWc2NGY1ZDU8ZjlmNzZlO0BpamZkOWo5cnI4NDMzbzczNUAyYzVgMC4yXy0xNGJhXy80YSNfMmtjMmRrY2dhLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796683&l=20250715235711846F1FF378B2EF24DC22&ply_type=2&policy=2&signature=43ea6d04b5f93253b2ea44b2c2958515&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CrjofomLFNq-U6Gi3fDG4S0DQEKQH5EhEC5Aobx6uoCI%7C1752623831%7Cf89f9b2f0650f209d9509d81e310fb345cced4a68d8c98c502afd139395758cb; Domain=.tiktok.com; Path=/; Expires=1783727831; tt_csrf_token=URSFWvE6-yDWXk4ZB6J39v6XBQv_3fSsfJa4; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"KduUjn/T5YhYPcky+bLzMg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175831", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522797171754716438"}, "format": "h264_540p_832743-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_832743-1", "tbr": 832, "quality": 1, "preference": -1, "filesize": 5508286, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/ogImEQLQjW6zAxDQeFNqfslaXfgIiEFAIiKEDA/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1626&bt=813&cs=0&ds=6&ft=4KJMyMzm8Zmo0wsmPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=0&rc=aWc2NGY1ZDU8ZjlmNzZlO0BpamZkOWo5cnI4NDMzbzczNUAyYzVgMC4yXy0xNGJhXy80YSNfMmtjMmRrY2dhLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796683&l=20250715235711846F1FF378B2EF24DC22&ply_type=2&policy=2&signature=43ea6d04b5f93253b2ea44b2c2958515&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CrjofomLFNq-U6Gi3fDG4S0DQEKQH5EhEC5Aobx6uoCI%7C1752623831%7Cf89f9b2f0650f209d9509d81e310fb345cced4a68d8c98c502afd139395758cb; Domain=.tiktok.com; Path=/; Expires=1783727831; tt_csrf_token=URSFWvE6-yDWXk4ZB6J39v6XBQv_3fSsfJa4; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"KduUjn/T5YhYPcky+bLzMg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175831", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522797171754716438"}, "format": "h264_540p_832743-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_389133-0", "tbr": 389, "quality": 1, "preference": -1, "filesize": 2573971, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/ooXjgzEF6CeETmIZIafiWGAIDfAGFLQsiDdQKE/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=760&bt=380&cs=2&ds=6&ft=4KJMyMzm8Zmo0wsmPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=11&rc=Nmk2N2Y7NDk4OzM6OGY2aEBpamZkOWo5cnI4NDMzbzczNUBgYmIuL2FgXmMxYTEvY14uYSNfMmtjMmRrY2dhLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796683&l=20250715235711846F1FF378B2EF24DC22&ply_type=2&policy=2&signature=5a0ae8f2566b06a53fb70ef0f0d488cd&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CrjofomLFNq-U6Gi3fDG4S0DQEKQH5EhEC5Aobx6uoCI%7C1752623831%7Cf89f9b2f0650f209d9509d81e310fb345cced4a68d8c98c502afd139395758cb; Domain=.tiktok.com; Path=/; Expires=1783727831; tt_csrf_token=URSFWvE6-yDWXk4ZB6J39v6XBQv_3fSsfJa4; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"KduUjn/T5YhYPcky+bLzMg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175831", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522797171754716438"}, "format": "bytevc1_540p_389133-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_389133-1", "tbr": 389, "quality": 1, "preference": -1, "filesize": 2573971, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/ooXjgzEF6CeETmIZIafiWGAIDfAGFLQsiDdQKE/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=760&bt=380&cs=2&ds=6&ft=4KJMyMzm8Zmo0wsmPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=11&rc=Nmk2N2Y7NDk4OzM6OGY2aEBpamZkOWo5cnI4NDMzbzczNUBgYmIuL2FgXmMxYTEvY14uYSNfMmtjMmRrY2dhLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796683&l=20250715235711846F1FF378B2EF24DC22&ply_type=2&policy=2&signature=5a0ae8f2566b06a53fb70ef0f0d488cd&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CrjofomLFNq-U6Gi3fDG4S0DQEKQH5EhEC5Aobx6uoCI%7C1752623831%7Cf89f9b2f0650f209d9509d81e310fb345cced4a68d8c98c502afd139395758cb; Domain=.tiktok.com; Path=/; Expires=1783727831; tt_csrf_token=URSFWvE6-yDWXk4ZB6J39v6XBQv_3fSsfJa4; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"KduUjn/T5YhYPcky+bLzMg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175831", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522797171754716438"}, "format": "bytevc1_540p_389133-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_494631-0", "tbr": 494, "quality": 2, "preference": -1, "filesize": 3271799, "width": 720, "height": 1280, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oIfKODzPIAIi8YgIgLsEvFfGtQbjAPF1QMD1uf/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=966&bt=483&cs=2&ds=3&ft=4KJMyMzm8Zmo0wsmPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=14&rc=NGlkOzk1ZDg8OTczZDs4ZkBpamZkOWo5cnI4NDMzbzczNUAwYGEvMGMvXl8xNS4yMDMwYSNfMmtjMmRrY2dhLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796683&l=20250715235711846F1FF378B2EF24DC22&ply_type=2&policy=2&signature=0aac1d77ff7c896226beed7d08d4f9b7&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CrjofomLFNq-U6Gi3fDG4S0DQEKQH5EhEC5Aobx6uoCI%7C1752623831%7Cf89f9b2f0650f209d9509d81e310fb345cced4a68d8c98c502afd139395758cb; Domain=.tiktok.com; Path=/; Expires=1783727831; tt_csrf_token=URSFWvE6-yDWXk4ZB6J39v6XBQv_3fSsfJa4; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"KduUjn/T5YhYPcky+bLzMg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175831", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522797171754716438"}, "format": "bytevc1_720p_494631-0 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_494631-1", "tbr": 494, "quality": 2, "preference": -1, "filesize": 3271799, "width": 720, "height": 1280, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oIfKODzPIAIi8YgIgLsEvFfGtQbjAPF1QMD1uf/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=966&bt=483&cs=2&ds=3&ft=4KJMyMzm8Zmo0wsmPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=14&rc=NGlkOzk1ZDg8OTczZDs4ZkBpamZkOWo5cnI4NDMzbzczNUAwYGEvMGMvXl8xNS4yMDMwYSNfMmtjMmRrY2dhLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796683&l=20250715235711846F1FF378B2EF24DC22&ply_type=2&policy=2&signature=0aac1d77ff7c896226beed7d08d4f9b7&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CrjofomLFNq-U6Gi3fDG4S0DQEKQH5EhEC5Aobx6uoCI%7C1752623831%7Cf89f9b2f0650f209d9509d81e310fb345cced4a68d8c98c502afd139395758cb; Domain=.tiktok.com; Path=/; Expires=1783727831; tt_csrf_token=URSFWvE6-yDWXk4ZB6J39v6XBQv_3fSsfJa4; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"KduUjn/T5YhYPcky+bLzMg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175831", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522797171754716438"}, "format": "bytevc1_720p_494631-1 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_765655-0", "tbr": 765, "quality": 3, "preference": -1, "filesize": 5064526, "width": 1080, "height": 1920, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oUse1DfIXEFKLQifIAgmaIQaWFjizA22EE6DQk/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1494&bt=747&cs=2&ds=4&ft=4KJMyMzm8Zmo0wsmPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=15&rc=OzU5ZDpkOGQ7NDk7NDRoOkBpamZkOWo5cnI4NDMzbzczNUBgNjMvNjBhNi8xYDA1MGFfYSNfMmtjMmRrY2dhLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796683&l=20250715235711846F1FF378B2EF24DC22&ply_type=2&policy=2&signature=d90e0713d6a2fc0c3df497df461d5670&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CrjofomLFNq-U6Gi3fDG4S0DQEKQH5EhEC5Aobx6uoCI%7C1752623831%7Cf89f9b2f0650f209d9509d81e310fb345cced4a68d8c98c502afd139395758cb; Domain=.tiktok.com; Path=/; Expires=1783727831; tt_csrf_token=URSFWvE6-yDWXk4ZB6J39v6XBQv_3fSsfJa4; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"KduUjn/T5YhYPcky+bLzMg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175831", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522797171754716438"}, "format": "bytevc1_1080p_765655-0 - 1080x1920"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_765655-1", "tbr": 765, "quality": 3, "preference": -1, "filesize": 5064526, "width": 1080, "height": 1920, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oUse1DfIXEFKLQifIAgmaIQaWFjizA22EE6DQk/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1494&bt=747&cs=2&ds=4&ft=4KJMyMzm8Zmo0wsmPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=15&rc=OzU5ZDpkOGQ7NDk7NDRoOkBpamZkOWo5cnI4NDMzbzczNUBgNjMvNjBhNi8xYDA1MGFfYSNfMmtjMmRrY2dhLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796683&l=20250715235711846F1FF378B2EF24DC22&ply_type=2&policy=2&signature=d90e0713d6a2fc0c3df497df461d5670&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CrjofomLFNq-U6Gi3fDG4S0DQEKQH5EhEC5Aobx6uoCI%7C1752623831%7Cf89f9b2f0650f209d9509d81e310fb345cced4a68d8c98c502afd139395758cb; Domain=.tiktok.com; Path=/; Expires=1783727831; tt_csrf_token=URSFWvE6-yDWXk4ZB6J39v6XBQv_3fSsfJa4; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"KduUjn/T5YhYPcky+bLzMg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175831", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522797171754716438"}, "format": "bytevc1_1080p_765655-1 - 1080x1920"}], "subtitles": {}, "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7522797171754716438"}, "channel": "<PERSON>", "channel_id": "MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader": "dylan.page", "uploader_id": "6760368158794155013", "channel_url": "https://www.tiktok.com/@MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader_url": "https://www.tiktok.com/@dylan.page", "track": "original sound", "artists": ["<PERSON>"], "duration": 52, "title": "So messed up man 😪", "description": "So messed up man 😪", "timestamp": 1751537715, "view_count": 9500000, "like_count": 1400000, "repost_count": 48500, "comment_count": 9619, "thumbnails": [{"id": "dynamicCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oYyBEIkelGADWLAKALLCLjQABIeAtFGzIzeOmI~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=fV1g%2BmeGjmWNRoaWow3pHQfJYKk%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -2}, {"id": "cover", "url": "https://p19-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oYyBEIkelGADWLAKALLCLjQABIeAtFGzIzeOmI~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=ODpOX8p%2B6YxlL2DRZrrnoheUbgY%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}, {"id": "originCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oIIBmQiI4DKAhAjag6IAtFELfWD8szBUefXiDI~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=b9%2BqQjtmDZYgepD%2Fi8Bh1jf2NuU%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}], "webpage_url": "https://www.tiktok.com/@dylan.page/video/7522797171754716438", "webpage_url_basename": "7522797171754716438", "webpage_url_domain": "tiktok.com", "extractor": "TikTok", "extractor_key": "TikTok", "thumbnail": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oIIBmQiI4DKAhAjag6IAtFELfWD8szBUefXiDI~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=b9%2BqQjtmDZYgepD%2Fi8Bh1jf2NuU%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "display_id": "7522797171754716438", "fulltitle": "So messed up man 😪", "duration_string": "52", "upload_date": "20250703", "artist": "<PERSON>", "epoch": 1752623831, "ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_389133-1", "tbr": 389, "quality": 1, "preference": -1, "filesize": 2573971, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/ooXjgzEF6CeETmIZIafiWGAIDfAGFLQsiDdQKE/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=760&bt=380&cs=2&ds=6&ft=4KJMyMzm8Zmo0wsmPI4jVLmuQpWrKsd.&mime_type=video_mp4&qs=11&rc=Nmk2N2Y7NDk4OzM6OGY2aEBpamZkOWo5cnI4NDMzbzczNUBgYmIuL2FgXmMxYTEvY14uYSNfMmtjMmRrY2dhLS1kMTFzcw%3D%3D&btag=e00088000&expire=1752796683&l=20250715235711846F1FF378B2EF24DC22&ply_type=2&policy=2&signature=5a0ae8f2566b06a53fb70ef0f0d488cd&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7CrjofomLFNq-U6Gi3fDG4S0DQEKQH5EhEC5Aobx6uoCI%7C1752623831%7Cf89f9b2f0650f209d9509d81e310fb345cced4a68d8c98c502afd139395758cb; Domain=.tiktok.com; Path=/; Expires=1783727831; tt_csrf_token=URSFWvE6-yDWXk4ZB6J39v6XBQv_3fSsfJa4; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"KduUjn/T5YhYPcky+bLzMg==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175831", "format": "bytevc1_540p_389133-1 - 576x1024", "_type": "video", "_version": {"version": "2025.06.30", "release_git_head": "b0187844988e557c7e1e6bb1aabd4c1176768d86", "repository": "yt-dlp/yt-dlp"}}