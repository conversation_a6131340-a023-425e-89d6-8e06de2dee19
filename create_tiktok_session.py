#!/usr/bin/env python3
"""
TikTok Session Creator for Headless Servers
Creates TikTok authentication session without browser
"""
import os
import json
import requests
from pathlib import Path
import getpass

def create_tiktok_session():
    """
    Create TikTok session for headless server
    """
    print("🎵 TikTok Session Creator (Headless Server)")
    print("=" * 50)
    print()
    print("⚠️  WARNING: TikTok authentication is complex and may require 2FA")
    print("This is an experimental feature for headless servers.")
    print()
    
    # Create session directory
    session_dir = Path.home() / ".social_tracker" / "sessions"
    session_dir.mkdir(parents=True, exist_ok=True)
    
    print("📋 TikTok Login Options:")
    print("1. Manual cookies (recommended)")
    print("2. Skip TikTok authentication (public only)")
    print()
    
    choice = input("Choose option (1-2): ").strip()
    
    if choice == "1":
        return setup_manual_cookies(session_dir)
    elif choice == "2":
        print("⚠️  Skipping TikTok authentication - only public content accessible")
        return None
    else:
        print("❌ Invalid choice")
        return None

def setup_manual_cookies(session_dir):
    """Setup manual cookies entry"""
    print()
    print("📋 MANUAL COOKIES SETUP:")
    print("Since this is a headless server, you need to get cookies from another machine:")
    print()
    print("1. On a machine with browser:")
    print("   - Go to tiktok.com and log in")
    print("   - Open browser developer tools (F12)")
    print("   - Go to Application/Storage > Cookies > https://tiktok.com")
    print("   - Copy important cookies (sessionid, tt_webid, etc.)")
    print()
    print("2. Enter cookies in Netscape format:")
    print("   # Netscape HTTP Cookie File")
    print("   .tiktok.com	TRUE	/	FALSE	0	sessionid	your_session_id")
    print("   .tiktok.com	TRUE	/	FALSE	0	tt_webid	your_webid")
    print()
    
    cookies_file = session_dir / "tiktok_cookies.txt"
    
    print(f"📝 Enter cookies (one per line, empty line to finish):")
    print(f"Cookies will be saved to: {cookies_file}")
    print()
    
    cookies_content = ["# Netscape HTTP Cookie File"]
    
    while True:
        line = input("Cookie line (or press Enter to finish): ").strip()
        if not line:
            break
        cookies_content.append(line)
    
    if len(cookies_content) > 1:
        try:
            with open(cookies_file, 'w') as f:
                f.write('\n'.join(cookies_content))
            
            print(f"✅ Cookies saved to: {cookies_file}")
            
            # Update config
            update_config_with_cookies(str(cookies_file))
            
            return str(cookies_file)
            
        except Exception as e:
            print(f"❌ Failed to save cookies: {e}")
            return None
    else:
        print("❌ No cookies entered")
        return None

def update_config_with_cookies(cookies_path):
    """Update config.py with cookies path"""
    try:
        config_path = Path(__file__).parent / "config.py"
        
        if config_path.exists():
            # Read config
            with open(config_path, 'r') as f:
                content = f.read()
            
            # Update TIKTOK_COOKIES_FILE
            if 'TIKTOK_COOKIES_FILE = None' in content:
                content = content.replace(
                    'TIKTOK_COOKIES_FILE = None',
                    f'TIKTOK_COOKIES_FILE = "{cookies_path}"'
                )
                
                # Write back
                with open(config_path, 'w') as f:
                    f.write(content)
                
                print("✅ Config updated with cookies path")
            else:
                print("⚠️  Please manually update config.py:")
                print(f"   TIKTOK_COOKIES_FILE = \"{cookies_path}\"")
                
    except Exception as e:
        print(f"⚠️  Could not update config automatically: {e}")
        print(f"Please manually update config.py:")
        print(f"   TIKTOK_COOKIES_FILE = \"{cookies_path}\"")

def test_tiktok_session():
    """Test TikTok session"""
    try:
        from tiktok_downloader import TikTokDownloader
        import config
        
        cookies_file = getattr(config, 'TIKTOK_COOKIES_FILE', None)
        
        if cookies_file and Path(cookies_file).exists():
            downloader = TikTokDownloader(cookies_file=cookies_file)
            print("✅ TikTok downloader initialized with cookies")
            print("🔓 Private TikTok users should now be accessible")
        else:
            downloader = TikTokDownloader()
            print("✅ TikTok downloader initialized without cookies")
            print("🔒 Only public TikTok users will be accessible")
            
    except Exception as e:
        print(f"❌ TikTok downloader test failed: {e}")

if __name__ == "__main__":
    print("🎵 TikTok Authentication Setup (Headless Server)")
    print("=" * 60)
    
    cookies_path = create_tiktok_session()
    
    if cookies_path:
        print()
        print("🧪 Testing TikTok authentication...")
        test_tiktok_session()
    
    print()
    print("🎯 NEXT STEPS:")
    print("1. Test with a TikTok user:")
    print("   python3 auto_tracker.py add --platform tiktok --username username")
    print("   python3 auto_tracker.py check --platform tiktok --username username")
    print()
    print("2. Test with all TikTok users:")
    print("   python3 auto_tracker.py check --platform tiktok --username allusers")
    print()
    print("📝 NOTE: For private users, you must follow them on TikTok first.")
