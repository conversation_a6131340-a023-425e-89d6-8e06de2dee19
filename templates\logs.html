{% extends "base.html" %}

{% block title %}Download Logs - Social Media Tracker{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">📥 Download Logs</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-primary btn-custom" onclick="location.reload()">
                <i class="fas fa-sync me-1"></i> Refresh
            </button>
        </div>
    </div>
</div>

<!-- Filter Controls -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="platformFilter" class="form-label">Platform</label>
                <select class="form-select" id="platformFilter" onchange="filterLogs()">
                    <option value="">All Platforms</option>
                    <option value="instagram">Instagram</option>
                    <option value="tiktok">TikTok</option>
                    <option value="vsco">VSCO</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="userFilter" class="form-label">User</label>
                <input type="text" class="form-control" id="userFilter" placeholder="Filter by username" onkeyup="filterLogs()">
            </div>
            <div class="col-md-3">
                <label for="contentTypeFilter" class="form-label">Content Type</label>
                <select class="form-select" id="contentTypeFilter" onchange="filterLogs()">
                    <option value="">All Types</option>
                    <option value="post">Posts</option>
                    <option value="story">Stories</option>
                    <option value="reel">Reels</option>
                    <option value="video">Videos</option>
                    <option value="image">Images</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="dateFilter" class="form-label">Date Range</label>
                <select class="form-select" id="dateFilter" onchange="filterLogs()">
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Downloads Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Recent Downloads ({{ downloads|length }} items)</h5>
    </div>
    <div class="card-body">
        {% if downloads %}
            <div class="table-responsive">
                <table class="table table-hover" id="downloadsTable">
                    <thead>
                        <tr>
                            <th>Platform</th>
                            <th>User</th>
                            <th>Content ID</th>
                            <th>Type</th>
                            <th>File Path</th>
                            <th>Downloaded</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for download in downloads %}
                        <tr data-platform="{{ download.platform }}" data-username="{{ download.username }}" data-type="{{ download.content_type }}" data-date="{{ download.download_time }}">
                            <td>
                                {% if download.platform == 'instagram' %}
                                    <span class="badge bg-danger">
                                        <i class="fab fa-instagram me-1"></i>Instagram
                                    </span>
                                {% elif download.platform == 'tiktok' %}
                                    <span class="badge bg-dark">
                                        <i class="fab fa-tiktok me-1"></i>TikTok
                                    </span>
                                {% else %}
                                    <span class="badge bg-info">
                                        <i class="fas fa-camera me-1"></i>VSCO
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <strong>@{{ download.username }}</strong>
                            </td>
                            <td>
                                <code class="small">{{ download.content_id[:30] }}{% if download.content_id|length > 30 %}...{% endif %}</code>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ download.content_type }}</span>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {% if download.file_path %}
                                        {{ download.file_path.split('/')[-1] }}
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </small>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ download.download_time }}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    {% if download.file_path %}
                                    <button type="button" class="btn btn-outline-primary" onclick="viewFile('{{ download.file_path }}')" title="View File">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% endif %}
                                    <button type="button" class="btn btn-outline-info" onclick="showDetails('{{ download.id }}')" title="Details">
                                        <i class="fas fa-info"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination would go here if needed -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="text-muted">
                    Showing {{ downloads|length }} downloads
                </div>
                <div>
                    <button class="btn btn-outline-primary btn-sm" onclick="exportLogs()">
                        <i class="fas fa-download me-1"></i> Export CSV
                    </button>
                </div>
            </div>
        {% else %}
            <div class="text-center text-muted py-5">
                <i class="fas fa-inbox fa-4x mb-3"></i>
                <h4>No Downloads Yet</h4>
                <p>Downloads will appear here once users start being tracked.</p>
                <a href="{{ url_for('users') }}" class="btn btn-primary btn-custom">
                    <i class="fas fa-plus me-1"></i> Add Users
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Download Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Download Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailsContent">
                <!-- Details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function filterLogs() {
    const platformFilter = document.getElementById('platformFilter').value.toLowerCase();
    const userFilter = document.getElementById('userFilter').value.toLowerCase();
    const contentTypeFilter = document.getElementById('contentTypeFilter').value.toLowerCase();
    const dateFilter = document.getElementById('dateFilter').value;
    
    const rows = document.querySelectorAll('#downloadsTable tbody tr');
    let visibleCount = 0;
    
    rows.forEach(row => {
        const platform = row.dataset.platform.toLowerCase();
        const username = row.dataset.username.toLowerCase();
        const contentType = row.dataset.type.toLowerCase();
        const downloadDate = new Date(row.dataset.date);
        
        let showRow = true;
        
        // Platform filter
        if (platformFilter && !platform.includes(platformFilter)) {
            showRow = false;
        }
        
        // User filter
        if (userFilter && !username.includes(userFilter)) {
            showRow = false;
        }
        
        // Content type filter
        if (contentTypeFilter && !contentType.includes(contentTypeFilter)) {
            showRow = false;
        }
        
        // Date filter
        if (dateFilter) {
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            
            switch (dateFilter) {
                case 'today':
                    if (downloadDate < today) showRow = false;
                    break;
                case 'week':
                    if (downloadDate < weekAgo) showRow = false;
                    break;
                case 'month':
                    if (downloadDate < monthAgo) showRow = false;
                    break;
            }
        }
        
        row.style.display = showRow ? '' : 'none';
        if (showRow) visibleCount++;
    });
    
    // Update count
    document.querySelector('.card-title').textContent = `Recent Downloads (${visibleCount} items)`;
}

function viewFile(filePath) {
    // This would open the file in a new window/tab
    // For security reasons, we'll just show an alert with the path
    showAlert(`File location: ${filePath}`, 'info');
}

function showDetails(downloadId) {
    // Show download details in modal
    const detailsContent = document.getElementById('detailsContent');
    detailsContent.innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">Loading details...</p>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
    modal.show();
    
    // Simulate loading details (in real implementation, this would be an API call)
    setTimeout(() => {
        detailsContent.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Download Information</h6>
                    <p><strong>ID:</strong> ${downloadId}</p>
                    <p><strong>Status:</strong> <span class="badge bg-success">Completed</span></p>
                    <p><strong>File Size:</strong> 2.4 MB</p>
                </div>
                <div class="col-md-6">
                    <h6>Metadata</h6>
                    <p><strong>Resolution:</strong> 1080x1920</p>
                    <p><strong>Duration:</strong> 15 seconds</p>
                    <p><strong>Format:</strong> MP4</p>
                </div>
            </div>
        `;
    }, 1000);
}

function exportLogs() {
    // Export visible logs to CSV
    const rows = document.querySelectorAll('#downloadsTable tbody tr:not([style*="display: none"])');
    let csv = 'Platform,User,Content ID,Type,File Path,Downloaded\n';
    
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        const platform = row.dataset.platform;
        const username = row.dataset.username;
        const contentId = cells[2].textContent.trim();
        const type = row.dataset.type;
        const filePath = cells[4].textContent.trim();
        const downloaded = cells[5].textContent.trim();
        
        csv += `"${platform}","${username}","${contentId}","${type}","${filePath}","${downloaded}"\n`;
    });
    
    // Download CSV
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `social_tracker_logs_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    showAlert('Logs exported successfully!', 'success');
}
</script>
{% endblock %}
