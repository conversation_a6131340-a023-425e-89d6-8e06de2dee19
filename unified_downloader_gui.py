#!/usr/bin/env python3
"""
Unified Social Media Downloader - GUI Application for Instagram and TikTok
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import queue
import os
from pathlib import Path
from typing import Optional, Dict, Any
import logging
from datetime import datetime

# Import our modules
from instagram_downloader import InstagramDownloader
from tiktok_downloader import TikTokDownloader
from vimeo_downloader import VimeoDownloader
from error_handler import InstagramDownloaderError, AuthenticationError, NetworkError, ContentError
from progress_tracker import ProgressTracker, ProgressWidget, DownloadStatus, DetailedProgressDialog
from gui_styles import (
    ModernStyle, configure_modern_style, create_card_frame, create_icon_button,
    create_status_label, add_separator, ToolTip, create_modern_scrollable_frame
)
import config


class UnifiedDownloaderGUI:
    """Unified GUI application for Instagram and TikTok downloading"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("📱 Social Media Downloader - Instagram, TikTok & Vimeo")
        self.root.geometry("950x800")
        self.root.minsize(750, 650)
        
        # Set window styling
        self.setup_window_styling()
        
        # Configure modern style
        self.style = configure_modern_style()
        
        # Application state
        self.instagram_downloader: Optional[InstagramDownloader] = None
        self.tiktok_downloader: Optional[TikTokDownloader] = None
        self.vimeo_downloader: Optional[VimeoDownloader] = None
        self.download_thread: Optional[threading.Thread] = None
        self.message_queue = queue.Queue()
        self.is_downloading = False
        self.progress_tracker: Optional[ProgressTracker] = None
        self.progress_dialog: Optional[DetailedProgressDialog] = None
        
        # Variables
        self.platform_var = tk.StringVar(value="instagram")
        self.username_var = tk.StringVar()
        self.url_var = tk.StringVar()
        self.login_username_var = tk.StringVar()
        self.login_password_var = tk.StringVar()
        self.output_dir_var = tk.StringVar(value=str(config.DOWNLOADS_DIR))
        self.count_limit_var = tk.StringVar()

        # Instagram-specific variables
        self.download_posts_var = tk.BooleanVar(value=True)
        self.download_stories_var = tk.BooleanVar(value=True)
        self.download_reels_var = tk.BooleanVar(value=True)
        self.download_highlights_var = tk.BooleanVar(value=True)

        # TikTok-specific variables
        self.tiktok_quality_var = tk.StringVar(value="best")
        self.save_metadata_var = tk.BooleanVar(value=True)

        # Vimeo-specific variables
        self.vimeo_quality_var = tk.StringVar(value="best")
        self.vimeo_api_token_var = tk.StringVar()
        self.download_playlists_var = tk.BooleanVar(value=False)
        
        # Setup GUI
        self.setup_gui()
        self.setup_logging()
        
        # Start message processing
        self.process_messages()
    
    def setup_window_styling(self):
        """Setup window styling and appearance"""
        self.root.configure(bg=ModernStyle.COLORS['gray_100'])
        self.center_window()
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_gui(self):
        """Setup the main GUI components"""
        # Create main container
        main_container = ttk.Frame(self.root, padding=ModernStyle.SPACING['md'])
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # Create header
        self.create_header(main_container)
        
        # Create main notebook for tabs
        self.notebook = ttk.Notebook(main_container)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(ModernStyle.SPACING['md'], 0))
        
        # Create tabs
        self.create_download_tab()
        self.create_batch_download_tab()
        self.create_settings_tab()
        self.create_log_tab()
    
    def create_header(self, parent):
        """Create application header"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, ModernStyle.SPACING['md']))
        
        # Title
        title_label = ttk.Label(header_frame, text="📱 Social Media Downloader",
                               style='Heading.TLabel')
        title_label.pack(side=tk.LEFT)

        # Platform selector
        platform_frame = ttk.Frame(header_frame)
        platform_frame.pack(side=tk.RIGHT)

        ttk.Label(platform_frame, text="Platform:", style='Subheading.TLabel').pack(side=tk.LEFT,
                                                                                    padx=(0, ModernStyle.SPACING['xs']))

        platform_combo = ttk.Combobox(platform_frame, textvariable=self.platform_var,
                                     values=["instagram", "tiktok", "vimeo"], state="readonly", width=12)
        platform_combo.pack(side=tk.LEFT)
        platform_combo.bind('<<ComboboxSelected>>', self.on_platform_change)
        
        # Status indicator
        self.connection_status = create_status_label(header_frame, "Ready", "info")
        self.connection_status.pack(side=tk.RIGHT, padx=(ModernStyle.SPACING['md'], 0))
    
    def create_download_tab(self):
        """Create the main download tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="🚀 Download")
        
        # Create scrollable area
        canvas, scrollbar, scrollable_frame = create_modern_scrollable_frame(tab_frame)
        
        # Platform selection card
        platform_card = create_card_frame(scrollable_frame, "📱 Platform Selection", ModernStyle.SPACING['lg'])
        platform_card.pack(fill=tk.X, padx=ModernStyle.SPACING['md'], pady=ModernStyle.SPACING['sm'])
        
        platform_buttons_frame = ttk.Frame(platform_card)
        platform_buttons_frame.pack(fill=tk.X)
        
        self.instagram_btn = create_icon_button(platform_buttons_frame, "Instagram", 
                                               lambda: self.set_platform("instagram"), 
                                               'Primary.TButton', "📸")
        self.instagram_btn.pack(side=tk.LEFT, padx=(0, ModernStyle.SPACING['sm']))
        
        self.tiktok_btn = create_icon_button(platform_buttons_frame, "TikTok",
                                            lambda: self.set_platform("tiktok"),
                                            'TButton', "🎵")
        self.tiktok_btn.pack(side=tk.LEFT, padx=(0, ModernStyle.SPACING['sm']))

        self.vimeo_btn = create_icon_button(platform_buttons_frame, "Vimeo",
                                           lambda: self.set_platform("vimeo"),
                                           'TButton', "🎬")
        self.vimeo_btn.pack(side=tk.LEFT)
        
        # Target input card
        target_card = create_card_frame(scrollable_frame, "🎯 Target", ModernStyle.SPACING['lg'])
        target_card.pack(fill=tk.X, padx=ModernStyle.SPACING['md'], pady=ModernStyle.SPACING['sm'])
        
        # Username input
        username_frame = ttk.Frame(target_card)
        username_frame.pack(fill=tk.X, pady=(0, ModernStyle.SPACING['sm']))
        
        ttk.Label(username_frame, text="Username:", style='Subheading.TLabel').pack(anchor=tk.W)
        
        username_input_frame = ttk.Frame(username_frame)
        username_input_frame.pack(fill=tk.X, pady=(ModernStyle.SPACING['xs'], 0))
        
        self.username_entry = ttk.Entry(username_input_frame, textvariable=self.username_var, 
                                       font=ModernStyle.FONTS['default'])
        self.username_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, ModernStyle.SPACING['sm']))
        
        self.view_profile_btn = create_icon_button(username_input_frame, "View Profile", 
                                                  self.view_profile, 'Success.TButton', "👤")
        self.view_profile_btn.pack(side=tk.RIGHT)
        
        # URL input (alternative)
        url_frame = ttk.Frame(target_card)
        url_frame.pack(fill=tk.X)
        
        ttk.Label(url_frame, text="Or single URL:", style='Small.TLabel').pack(anchor=tk.W)
        
        url_input_frame = ttk.Frame(url_frame)
        url_input_frame.pack(fill=tk.X, pady=(ModernStyle.SPACING['xs'], 0))
        
        self.url_entry = ttk.Entry(url_input_frame, textvariable=self.url_var, 
                                  font=ModernStyle.FONTS['default'])
        self.url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, ModernStyle.SPACING['sm']))
        
        self.download_url_btn = create_icon_button(url_input_frame, "Download", 
                                                  self.download_single_url, 'Primary.TButton', "⬇️")
        self.download_url_btn.pack(side=tk.RIGHT)
        
        # Login section (Instagram only)
        self.login_card = create_card_frame(scrollable_frame, "🔐 Instagram Login (Optional)", 
                                           ModernStyle.SPACING['lg'])
        self.login_card.pack(fill=tk.X, padx=ModernStyle.SPACING['md'], pady=ModernStyle.SPACING['sm'])
        
        login_info = ttk.Label(self.login_card, text="Required for Instagram stories, highlights, and private accounts", 
                              style='Small.TLabel')
        login_info.pack(anchor=tk.W, pady=(0, ModernStyle.SPACING['sm']))
        
        login_grid = ttk.Frame(self.login_card)
        login_grid.pack(fill=tk.X)
        
        ttk.Label(login_grid, text="Username:").grid(row=0, column=0, sticky=tk.W, 
                                                    padx=(0, ModernStyle.SPACING['sm']))
        ttk.Entry(login_grid, textvariable=self.login_username_var, width=25).grid(row=0, column=1, sticky=tk.W)
        
        ttk.Label(login_grid, text="Password:").grid(row=1, column=0, sticky=tk.W, 
                                                    padx=(0, ModernStyle.SPACING['sm']))
        ttk.Entry(login_grid, textvariable=self.login_password_var, show="*", width=25).grid(row=1, column=1, sticky=tk.W)
        
        self.login_btn = create_icon_button(login_grid, "Login", self.login_instagram, 
                                           'Success.TButton', "🔑")
        self.login_btn.grid(row=0, column=2, rowspan=2, padx=(ModernStyle.SPACING['md'], 0))
        
        # Content options (platform-specific)
        self.content_card = create_card_frame(scrollable_frame, "📂 Content Options", ModernStyle.SPACING['lg'])
        self.content_card.pack(fill=tk.X, padx=ModernStyle.SPACING['md'], pady=ModernStyle.SPACING['sm'])
        
        # This will be populated based on platform
        self.setup_content_options()
        
        # Download controls
        controls_card = create_card_frame(scrollable_frame, "🎮 Download Controls", ModernStyle.SPACING['lg'])
        controls_card.pack(fill=tk.X, padx=ModernStyle.SPACING['md'], pady=ModernStyle.SPACING['sm'])
        
        # Count limit
        limit_frame = ttk.Frame(controls_card)
        limit_frame.pack(fill=tk.X, pady=(0, ModernStyle.SPACING['sm']))
        
        ttk.Label(limit_frame, text="Limit (optional):").pack(side=tk.LEFT)
        ttk.Entry(limit_frame, textvariable=self.count_limit_var, width=10).pack(side=tk.LEFT, 
                                                                                padx=(ModernStyle.SPACING['xs'], 0))
        
        # Control buttons
        controls_frame = ttk.Frame(controls_card)
        controls_frame.pack(fill=tk.X)
        
        self.download_btn = create_icon_button(controls_frame, "Start Download", 
                                              self.start_download, 'Primary.TButton', "⬇️")
        self.download_btn.pack(side=tk.LEFT, padx=(0, ModernStyle.SPACING['sm']))
        
        self.stop_btn = create_icon_button(controls_frame, "Stop", self.stop_download, 
                                          'Danger.TButton', "⏹️")
        self.stop_btn.pack(side=tk.LEFT, padx=(0, ModernStyle.SPACING['sm']))
        self.stop_btn.config(state=tk.DISABLED)
        
        self.open_folder_btn = create_icon_button(controls_frame, "Open Downloads", 
                                                 self.open_downloads_folder, 'TButton', "📁")
        self.open_folder_btn.pack(side=tk.LEFT)
        
        # Progress section
        progress_card = create_card_frame(scrollable_frame, "📊 Progress", ModernStyle.SPACING['lg'])
        progress_card.pack(fill=tk.X, padx=ModernStyle.SPACING['md'], pady=ModernStyle.SPACING['sm'])
        
        self.progress_widget = ProgressWidget(progress_card)
        
        detail_btn = create_icon_button(progress_card, "Show Details", 
                                       self.show_detailed_progress, 'TButton', "📋")
        detail_btn.pack(pady=(ModernStyle.SPACING['sm'], 0))
        
        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def setup_content_options(self):
        """Setup content options based on selected platform"""
        # Clear existing content options
        for widget in self.content_card.winfo_children():
            widget.destroy()
        
        if self.platform_var.get() == "instagram":
            self.setup_instagram_options()
        elif self.platform_var.get() == "tiktok":
            self.setup_tiktok_options()
        else:
            self.setup_vimeo_options()
    
    def setup_instagram_options(self):
        """Setup Instagram-specific content options"""
        content_grid = ttk.Frame(self.content_card)
        content_grid.pack(fill=tk.X)
        
        ttk.Checkbutton(content_grid, text="📸 Posts", variable=self.download_posts_var).grid(
            row=0, column=0, sticky=tk.W, padx=ModernStyle.SPACING['sm'], pady=ModernStyle.SPACING['xs'])
        
        ttk.Checkbutton(content_grid, text="📱 Stories", variable=self.download_stories_var).grid(
            row=0, column=1, sticky=tk.W, padx=ModernStyle.SPACING['sm'], pady=ModernStyle.SPACING['xs'])
        
        ttk.Checkbutton(content_grid, text="🎬 Reels", variable=self.download_reels_var).grid(
            row=1, column=0, sticky=tk.W, padx=ModernStyle.SPACING['sm'], pady=ModernStyle.SPACING['xs'])
        
        ttk.Checkbutton(content_grid, text="⭐ Highlights", variable=self.download_highlights_var).grid(
            row=1, column=1, sticky=tk.W, padx=ModernStyle.SPACING['sm'], pady=ModernStyle.SPACING['xs'])
    
    def setup_tiktok_options(self):
        """Setup TikTok-specific content options"""
        options_frame = ttk.Frame(self.content_card)
        options_frame.pack(fill=tk.X)
        
        # Quality selection
        quality_frame = ttk.Frame(options_frame)
        quality_frame.pack(fill=tk.X, pady=(0, ModernStyle.SPACING['sm']))
        
        ttk.Label(quality_frame, text="Quality:").pack(side=tk.LEFT)
        quality_combo = ttk.Combobox(quality_frame, textvariable=self.tiktok_quality_var,
                                    values=["best", "720p", "1080p", "worst"], state="readonly", width=10)
        quality_combo.pack(side=tk.LEFT, padx=(ModernStyle.SPACING['xs'], 0))
        
        # Metadata option
        ttk.Checkbutton(options_frame, text="💾 Save Metadata", variable=self.save_metadata_var).pack(anchor=tk.W)

    def setup_vimeo_options(self):
        """Setup Vimeo-specific content options"""
        options_frame = ttk.Frame(self.content_card)
        options_frame.pack(fill=tk.X)

        # Quality selection
        quality_frame = ttk.Frame(options_frame)
        quality_frame.pack(fill=tk.X, pady=(0, ModernStyle.SPACING['sm']))

        ttk.Label(quality_frame, text="Quality:").pack(side=tk.LEFT)
        quality_combo = ttk.Combobox(quality_frame, textvariable=self.vimeo_quality_var,
                                    values=["best", "720p", "1080p", "worst"], state="readonly", width=10)
        quality_combo.pack(side=tk.LEFT, padx=(ModernStyle.SPACING['xs'], 0))

        # API token (optional)
        api_frame = ttk.Frame(options_frame)
        api_frame.pack(fill=tk.X, pady=(0, ModernStyle.SPACING['sm']))

        ttk.Label(api_frame, text="API Token (optional):").pack(anchor=tk.W)
        ttk.Entry(api_frame, textvariable=self.vimeo_api_token_var, width=40, show="*").pack(fill=tk.X, pady=(ModernStyle.SPACING['xs'], 0))

        # Options
        ttk.Checkbutton(options_frame, text="💾 Save Metadata", variable=self.save_metadata_var).pack(anchor=tk.W)
        ttk.Checkbutton(options_frame, text="📋 Include Playlists", variable=self.download_playlists_var).pack(anchor=tk.W)

    def on_platform_change(self, event=None):
        """Handle platform change"""
        self.setup_content_options()

        # Show/hide Instagram login
        if self.platform_var.get() == "instagram":
            self.login_card.pack(fill=tk.X, padx=ModernStyle.SPACING['md'],
                                pady=ModernStyle.SPACING['sm'], before=self.content_card)
        else:
            self.login_card.pack_forget()

        # Update button styles
        platform = self.platform_var.get()
        self.instagram_btn.config(style='Primary.TButton' if platform == "instagram" else 'TButton')
        self.tiktok_btn.config(style='Primary.TButton' if platform == "tiktok" else 'TButton')
        self.vimeo_btn.config(style='Primary.TButton' if platform == "vimeo" else 'TButton')

    def set_platform(self, platform: str):
        """Set the active platform"""
        self.platform_var.set(platform)
        self.on_platform_change()

    def create_batch_download_tab(self):
        """Create batch download tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📋 Batch Download")

        # Placeholder for batch download functionality
        placeholder_label = ttk.Label(tab_frame, text="Batch download functionality - Coming soon!",
                                     style='Heading.TLabel')
        placeholder_label.pack(expand=True)

    def create_settings_tab(self):
        """Create settings tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="⚙️ Settings")

        # Placeholder for settings
        placeholder_label = ttk.Label(tab_frame, text="Settings panel - Coming soon!",
                                     style='Heading.TLabel')
        placeholder_label.pack(expand=True)

    def create_log_tab(self):
        """Create log tab"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="📋 Logs")

        # Log display
        log_card = create_card_frame(tab_frame, "📋 Download Logs", ModernStyle.SPACING['lg'])
        log_card.pack(fill=tk.BOTH, expand=True, padx=ModernStyle.SPACING['md'],
                     pady=ModernStyle.SPACING['sm'])

        self.log_text = scrolledtext.ScrolledText(log_card, height=20, width=80,
                                                 font=ModernStyle.FONTS['small'])
        self.log_text.pack(fill=tk.BOTH, expand=True, pady=(0, ModernStyle.SPACING['sm']))

        # Log controls
        log_controls = ttk.Frame(log_card)
        log_controls.pack(fill=tk.X)

        create_icon_button(log_controls, "Clear Logs", self.clear_logs,
                          'Warning.TButton', "🗑️").pack(side=tk.LEFT,
                                                        padx=(0, ModernStyle.SPACING['sm']))
        create_icon_button(log_controls, "Save Logs", self.save_logs,
                          'TButton', "💾").pack(side=tk.LEFT)

    def setup_logging(self):
        """Setup logging to display in GUI"""
        self.gui_handler = GUILogHandler(self.message_queue)
        self.gui_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        self.gui_handler.setFormatter(formatter)
        logging.getLogger().addHandler(self.gui_handler)

    def process_messages(self):
        """Process messages from the queue and update GUI"""
        try:
            while True:
                message = self.message_queue.get_nowait()
                self.log_text.insert(tk.END, message + '\n')
                self.log_text.see(tk.END)
        except queue.Empty:
            pass
        self.root.after(100, self.process_messages)

    def login_instagram(self):
        """Login to Instagram"""
        if self.platform_var.get() != "instagram":
            messagebox.showwarning("Platform Error", "Instagram login is only available for Instagram platform")
            return

        username = self.login_username_var.get().strip()
        password = self.login_password_var.get().strip()

        if not username or not password:
            messagebox.showwarning("Login Required", "Please enter both username and password")
            return

        try:
            self.connection_status.config(text="Logging in...", style='Warning.TLabel')
            self.login_btn.config(state=tk.DISABLED)
            self.root.update()

            self.instagram_downloader = InstagramDownloader(username, password)

            if self.instagram_downloader.logged_in:
                self.connection_status.config(text=f"✅ Logged in as {username}", style='Success.TLabel')
                messagebox.showinfo("Login Successful", f"Successfully logged in as {username}")
            else:
                self.connection_status.config(text="❌ Login failed", style='Danger.TLabel')
                messagebox.showerror("Login Failed", "Login failed. Please check your credentials.")

        except Exception as e:
            self.connection_status.config(text="❌ Login error", style='Danger.TLabel')
            messagebox.showerror("Login Error", f"Login failed: {str(e)}")
        finally:
            self.login_btn.config(state=tk.NORMAL)

    def view_profile(self):
        """View user profile"""
        username = self.username_var.get().strip()
        if not username:
            messagebox.showwarning("Username Required", "Please enter a username")
            return

        platform = self.platform_var.get()

        try:
            self.connection_status.config(text=f"Loading @{username}...", style='Warning.TLabel')
            self.view_profile_btn.config(state=tk.DISABLED)
            self.root.update()

            if platform == "instagram":
                if not self.instagram_downloader:
                    self.instagram_downloader = InstagramDownloader()
                # Show Instagram profile info with user ID
                try:
                    from profile_manager import ProfileManager
                    profile_manager = ProfileManager(self.instagram_downloader)
                    profile_info = profile_manager.load_profile(username)

                    profile_text = f"Instagram Profile: @{username}\n"
                    profile_text += f"User ID: {profile_info.get('user_id', 'N/A')}\n"
                    profile_text += f"Full Name: {profile_info.get('full_name', 'N/A')}\n"
                    profile_text += f"Followers: {profile_info.get('followers', 0):,}\n"
                    profile_text += f"Posts: {profile_info.get('posts_count', 0):,}"

                    messagebox.showinfo("Profile Info", profile_text)
                except Exception:
                    messagebox.showinfo("Profile Info", f"Instagram profile: @{username}\nUser ID: Loading...\nReady for download")

            elif platform == "tiktok":
                if not self.tiktok_downloader:
                    self.tiktok_downloader = TikTokDownloader()
                user_info = self.tiktok_downloader.get_user_info(username)

                profile_text = f"TikTok Profile: {user_info['title']}\n"
                profile_text += f"User ID: {user_info.get('user_id', 'N/A')}\n"
                profile_text += f"Videos: {user_info['video_count']}\n"
                profile_text += f"Description: {user_info['description']}"

                messagebox.showinfo("Profile Info", profile_text)

            else:  # Vimeo
                if not self.vimeo_downloader:
                    api_token = self.vimeo_api_token_var.get().strip() or None
                    self.vimeo_downloader = VimeoDownloader(api_token=api_token)
                user_info = self.vimeo_downloader.get_user_info(username)

                profile_text = f"Vimeo Profile: {user_info['name']}\n"
                profile_text += f"User ID: {user_info.get('user_id', 'N/A')}\n"
                profile_text += f"Videos: {user_info['video_count']}\n"
                profile_text += f"Bio: {user_info['bio']}\n"
                profile_text += f"Followers: {user_info['follower_count']}"

                messagebox.showinfo("Profile Info", profile_text)

            self.connection_status.config(text=f"✅ Profile loaded: @{username}", style='Success.TLabel')

        except Exception as e:
            self.connection_status.config(text="❌ Profile load failed", style='Danger.TLabel')
            messagebox.showerror("Profile Error", f"Failed to load profile: {str(e)}")
        finally:
            self.view_profile_btn.config(state=tk.NORMAL)

    def download_single_url(self):
        """Download single URL"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showwarning("URL Required", "Please enter a URL")
            return

        platform = self.platform_var.get()

        try:
            self.connection_status.config(text="Downloading...", style='Warning.TLabel')
            self.download_url_btn.config(state=tk.DISABLED)

            if platform == "instagram":
                messagebox.showinfo("Instagram URL", "Instagram URL download not yet implemented")
            elif platform == "tiktok":
                if not self.tiktok_downloader:
                    self.tiktok_downloader = TikTokDownloader()
                success = self.tiktok_downloader.download_video(url)
                if success:
                    self.connection_status.config(text="✅ Download completed", style='Success.TLabel')
                    messagebox.showinfo("Download Complete", "Video downloaded successfully!")
                else:
                    self.connection_status.config(text="❌ Download failed", style='Danger.TLabel')
                    messagebox.showerror("Download Failed", "Failed to download video")
            else:  # Vimeo
                if not self.vimeo_downloader:
                    api_token = self.vimeo_api_token_var.get().strip() or None
                    self.vimeo_downloader = VimeoDownloader(api_token=api_token)
                success = self.vimeo_downloader.download_video(url)
                if success:
                    self.connection_status.config(text="✅ Download completed", style='Success.TLabel')
                    messagebox.showinfo("Download Complete", "Video downloaded successfully!")
                else:
                    self.connection_status.config(text="❌ Download failed", style='Danger.TLabel')
                    messagebox.showerror("Download Failed", "Failed to download video")

        except Exception as e:
            self.connection_status.config(text="❌ Download error", style='Danger.TLabel')
            messagebox.showerror("Download Error", f"Download failed: {str(e)}")
        finally:
            self.download_url_btn.config(state=tk.NORMAL)

    def start_download(self):
        """Start download process"""
        username = self.username_var.get().strip()
        if not username:
            messagebox.showwarning("Username Required", "Please enter a username")
            return

        if self.is_downloading:
            messagebox.showwarning("Download in Progress", "A download is already in progress")
            return

        platform = self.platform_var.get()
        count_limit = None

        try:
            if self.count_limit_var.get().strip():
                count_limit = int(self.count_limit_var.get())
                if count_limit <= 0:
                    raise ValueError("Count limit must be positive")
        except ValueError as e:
            messagebox.showerror("Invalid Limit", f"Invalid count limit: {str(e)}")
            return

        self.start_download_thread(username, platform, count_limit)

    def start_download_thread(self, username: str, platform: str, count_limit: Optional[int]):
        """Start download in separate thread"""
        self.is_downloading = True
        self.download_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)

        self.connection_status.config(text=f"🔄 Downloading from @{username}...", style='Warning.TLabel')

        self.progress_tracker = ProgressTracker(self.on_progress_update)
        self.progress_widget.set_tracker(self.progress_tracker)

        self.download_thread = threading.Thread(
            target=self.download_worker,
            args=(username, platform, count_limit),
            daemon=True
        )
        self.download_thread.start()

    def download_worker(self, username: str, platform: str, count_limit: Optional[int]):
        """Download worker function"""
        try:
            self.progress_tracker.update_status(DownloadStatus.INITIALIZING,
                                               f"Initializing {platform} download from @{username}")

            if platform == "instagram":
                if not self.instagram_downloader:
                    self.instagram_downloader = InstagramDownloader()

                content_flags = {
                    'posts': self.download_posts_var.get(),
                    'stories': self.download_stories_var.get(),
                    'reels': self.download_reels_var.get(),
                    'highlights': self.download_highlights_var.get()
                }

                self.progress_tracker.update_status(DownloadStatus.DOWNLOADING,
                                                   f"Downloading Instagram content from @{username}")

                results = self.instagram_downloader.download_all(
                    username=username,
                    include_posts=content_flags['posts'],
                    include_stories=content_flags['stories'],
                    include_reels=content_flags['reels'],
                    include_highlights=content_flags['highlights'],
                    posts_count=count_limit,
                    reels_count=count_limit
                )

                success_count = sum(1 for success in results.values() if success)
                total_count = len(results)

            elif platform == "tiktok":
                if not self.tiktok_downloader:
                    self.tiktok_downloader = TikTokDownloader()

                self.progress_tracker.update_status(DownloadStatus.DOWNLOADING,
                                                   f"Downloading TikTok videos from @{username}")

                success = self.tiktok_downloader.download_user_videos(username, count_limit)
                success_count = 1 if success else 0
                total_count = 1

            else:  # Vimeo
                if not self.vimeo_downloader:
                    api_token = self.vimeo_api_token_var.get().strip() or None
                    self.vimeo_downloader = VimeoDownloader(api_token=api_token)

                self.progress_tracker.update_status(DownloadStatus.DOWNLOADING,
                                                   f"Downloading Vimeo videos from {username}")

                success = self.vimeo_downloader.download_user_videos(username, count_limit)
                success_count = 1 if success else 0
                total_count = 1

            self.progress_tracker.update_status(
                DownloadStatus.COMPLETED,
                f"Download completed: {success_count}/{total_count} successful"
            )

            self.root.after(0, self.download_completed, username, platform, success_count, total_count)

        except Exception as e:
            error_msg = f"Download failed: {str(e)}"
            self.progress_tracker.update_status(DownloadStatus.ERROR, error_msg)
            self.root.after(0, self.download_error, error_msg)

    def on_progress_update(self, progress_info):
        """Handle progress updates"""
        self.root.after(0, lambda: self.progress_widget.update_display(progress_info))
        if self.progress_dialog:
            self.root.after(0, lambda: self.progress_dialog.update_progress(progress_info))

    def show_detailed_progress(self):
        """Show detailed progress dialog"""
        if not self.progress_dialog:
            self.progress_dialog = DetailedProgressDialog(self.root, "📊 Download Progress")
            if self.progress_tracker:
                self.progress_dialog.set_tracker(self.progress_tracker)
        self.progress_dialog.show()

    def download_completed(self, username: str, platform: str, success_count: int, total_count: int):
        """Handle download completion"""
        self.is_downloading = False
        self.download_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)

        self.connection_status.config(text=f"✅ Download completed for @{username}",
                                     style='Success.TLabel')

        summary = (f"🎉 {platform.title()} download from @{username} completed!\n\n"
                  f"✅ Success rate: {success_count}/{total_count}\n"
                  f"📁 Files saved to: {config.DOWNLOADS_DIR}")

        self.progress_widget.show_completed(f"{success_count}/{total_count} successful")
        messagebox.showinfo("Download Complete", summary)

    def download_error(self, error_msg: str):
        """Handle download error"""
        self.is_downloading = False
        self.download_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)

        self.connection_status.config(text="❌ Download failed", style='Danger.TLabel')
        self.progress_widget.show_error(error_msg)
        messagebox.showerror("Download Error", f"❌ {error_msg}")

    def stop_download(self):
        """Stop download"""
        if self.download_thread and self.download_thread.is_alive():
            self.is_downloading = False
            self.connection_status.config(text="⏹️ Stopping download...", style='Warning.TLabel')
            messagebox.showinfo("Download Stopped", "Download stop requested.")

    def open_downloads_folder(self):
        """Open downloads folder"""
        try:
            import subprocess
            import platform

            path = Path(self.output_dir_var.get())
            path.mkdir(exist_ok=True)

            if platform.system() == "Windows":
                subprocess.run(["explorer", str(path)])
            elif platform.system() == "Darwin":
                subprocess.run(["open", str(path)])
            else:
                subprocess.run(["xdg-open", str(path)])

            self.connection_status.config(text="📁 Opened downloads folder", style='Success.TLabel')
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open folder: {str(e)}")

    def clear_logs(self):
        """Clear logs"""
        if messagebox.askyesno("Clear Logs", "Clear all log messages?"):
            self.log_text.delete(1.0, tk.END)
            self.connection_status.config(text="🗑️ Logs cleared", style='Warning.TLabel')

    def save_logs(self):
        """Save logs"""
        filename = filedialog.asksaveasfilename(
            title="Save Logs",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("Log files", "*.log"), ("All files", "*.*")],
            initialname=f"social_media_downloader_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                self.connection_status.config(text="💾 Logs saved", style='Success.TLabel')
                messagebox.showinfo("Logs Saved", f"✅ Logs saved to {filename}")
            except Exception as e:
                messagebox.showerror("Save Error", f"❌ Failed to save logs: {str(e)}")

    def run(self):
        """Run the application"""
        self.root.after(100, self.center_window)
        self.root.mainloop()


class GUILogHandler(logging.Handler):
    """GUI logging handler"""

    def __init__(self, message_queue):
        super().__init__()
        self.message_queue = message_queue

    def emit(self, record):
        try:
            message = self.format(record)
            self.message_queue.put(message)
        except Exception:
            self.handleError(record)


def main():
    """Main function"""
    try:
        app = UnifiedDownloaderGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("Application Error", f"❌ Failed to start application: {str(e)}")


if __name__ == "__main__":
    main()
