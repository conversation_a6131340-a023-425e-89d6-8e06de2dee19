"""
Configuration settings for Instagram Downloader
"""
import os
from pathlib import Path

# Base configuration
BASE_DIR = Path(__file__).parent
DOWNLOADS_DIR = BASE_DIR / "downloads"
LOGS_DIR = BASE_DIR / "logs"

# Create directories if they don't exist
DOWNLOADS_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# File naming configuration
FILENAME_FORMAT = "{username}_{date}_{media_type}_{index}"
DATE_FORMAT = "%Y%m%d_%H%M%S"

# Download settings
MAX_RETRIES = 3
RETRY_DELAY = 2  # seconds
CHUNK_SIZE = 8192  # bytes

# Supported media types
SUPPORTED_MEDIA_TYPES = {
    'posts': True,
    'stories': True,
    'reels': True,
    'highlights': True
}

# File extensions
IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp']
VIDEO_EXTENSIONS = ['.mp4', '.mov', '.avi']

# Logging configuration
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Rate limiting (to avoid Instagram blocks)
REQUEST_DELAY = 1.0  # seconds between requests
MAX_REQUESTS_PER_HOUR = 200

# Error handling
MAX_CONNECTION_RETRIES = 3
CONNECTION_TIMEOUT = 30  # seconds
BACKOFF_FACTOR = 2  # exponential backoff multiplier

# Instagram specific settings
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
SESSION_FILE = BASE_DIR / "session.json"

# Instagram authentication (choose one method)
INSTAGRAM_USERNAME = "<EMAIL>"  # Set your Instagram username
INSTAGRAM_PASSWORD = "666no777"  # Set your Instagram password
INSTAGRAM_SESSION_FILE = str(Path.home() / ".social_tracker" / "sessions" / "soundsbyeze_session")  # Path to Instagram session file (recommended)

# Content filtering
MIN_IMAGE_SIZE = 100  # pixels
MIN_VIDEO_DURATION = 1  # seconds
SKIP_DUPLICATE_CONTENT = True

# Advanced options
DOWNLOAD_METADATA = True
DOWNLOAD_COMMENTS = False
DOWNLOAD_CAPTIONS = True
COMPRESS_METADATA = False
