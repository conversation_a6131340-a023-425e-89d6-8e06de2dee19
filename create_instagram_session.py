#!/usr/bin/env python3
"""
Instagram Session Creator
Creates a session file for Instagram authentication to avoid rate limiting
"""
import os
import sys
import getpass
import instaloader
from pathlib import Path


def create_session():
    """Create Instagram session file"""
    print("🔐 Instagram Session Creator")
    print("=" * 50)
    print("This will create a session file to authenticate with Instagram")
    print("without triggering rate limits or requiring repeated logins.\n")
    
    # Get credentials
    username = input("Enter your Instagram username: ").strip()
    if not username:
        print("❌ Username is required!")
        return False
    
    password = getpass.getpass("Enter your Instagram password: ").strip()
    if not password:
        print("❌ Password is required!")
        return False
    
    # Create session directory
    session_dir = Path.home() / ".social_tracker" / "sessions"
    session_dir.mkdir(parents=True, exist_ok=True)
    
    session_file = session_dir / f"{username}_session"
    
    print(f"\n🔄 Logging in to Instagram as @{username}...")
    
    try:
        # Create instaloader instance
        loader = instaloader.Instaloader(
            download_videos=False,
            download_video_thumbnails=False,
            download_geotags=False,
            download_comments=False,
            save_metadata=False,
            compress_json=False
        )
        
        # Login
        loader.login(username, password)
        
        # Save session
        loader.save_session_to_file(str(session_file))
        
        print(f"✅ Session created successfully!")
        print(f"📁 Session file: {session_file}")
        print(f"\n🔧 To use this session, add to your config:")
        print(f"   INSTAGRAM_SESSION_FILE = '{session_file}'")
        print(f"\n📋 Or use with the tracker:")
        print(f"   python3 auto_tracker.py add --platform instagram --username target_user --session {session_file}")
        
        return True
        
    except instaloader.exceptions.BadCredentialsException:
        print("❌ Invalid username or password!")
        return False
        
    except instaloader.exceptions.TwoFactorAuthRequiredException:
        print("❌ Two-factor authentication is enabled on your account.")
        print("   Please disable 2FA temporarily or use app passwords.")
        return False
        
    except instaloader.exceptions.ConnectionException as e:
        print(f"❌ Connection error: {e}")
        print("   This might be due to rate limiting. Try again later.")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def main():
    """Main function"""
    print("Instagram Session Creator for Social Media Auto Tracker\n")
    
    if create_session():
        print("\n🎉 Session creation completed successfully!")
        print("\n💡 Tips:")
        print("   • Keep your session file secure and private")
        print("   • Session files expire after ~60 days of inactivity")
        print("   • You can create multiple sessions for different accounts")
        print("   • Use this session file to avoid Instagram rate limits")
    else:
        print("\n❌ Session creation failed!")
        print("\n🔄 You can try again or use alternative methods:")
        print("   • Check your internet connection")
        print("   • Verify your Instagram credentials")
        print("   • Try again in a few minutes if rate limited")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
