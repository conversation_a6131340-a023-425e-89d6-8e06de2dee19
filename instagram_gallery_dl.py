#!/usr/bin/env python3
"""
Alternative Instagram Downloader using gallery-dl
More reliable than instaloader for rate-limited environments
"""
import os
import json
import subprocess
import logging
from pathlib import Path
from typing import Optional, Dict, List, Any
from datetime import datetime

import config

class InstagramGalleryDL:
    """Alternative Instagram downloader using gallery-dl"""
    
    def __init__(self, username: Optional[str] = None, password: Optional[str] = None):
        """
        Initialize Instagram downloader with gallery-dl
        
        Args:
            username: Instagram username
            password: Instagram password
        """
        self.username = username
        self.password = password
        self.output_dir = config.DOWNLOADS_DIR
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Create gallery-dl config
        self._setup_gallery_dl_config()
        
        self.logger.info("Instagram Gallery-DL downloader initialized")
    
    def _setup_gallery_dl_config(self):
        """Setup gallery-dl configuration"""
        config_dir = Path.home() / ".config" / "gallery-dl"
        config_dir.mkdir(parents=True, exist_ok=True)
        
        config_file = config_dir / "config.json"
        
        gallery_config = {
            "extractor": {
                "instagram": {
                    "directory": ["{category}", "{username}"],
                    "filename": "{date:%Y%m%d_%H%M%S}_{shortcode}.{extension}",
                    "archive": str(Path.home() / ".social_tracker" / "instagram_archive.txt"),
                    "sleep": 5,  # 5 seconds between requests
                    "sleep-request": [2, 5],  # 2-5 seconds between API requests
                }
            }
        }
        
        # Add authentication if provided
        if self.username and self.password:
            gallery_config["extractor"]["instagram"]["username"] = self.username
            gallery_config["extractor"]["instagram"]["password"] = self.password
        
        # Write config
        with open(config_file, 'w') as f:
            json.dump(gallery_config, f, indent=2)
        
        self.logger.info(f"Gallery-dl config created: {config_file}")
    
    def download_user_posts(self, username: str, count: Optional[int] = None) -> Dict[str, Any]:
        """
        Download user posts using gallery-dl
        
        Args:
            username: Instagram username
            count: Maximum number of posts to download
            
        Returns:
            Dict with download results
        """
        username = username.strip().lstrip('@')
        
        try:
            # Create output directory
            user_dir = self.output_dir / "Instagram" / username / "posts"
            user_dir.mkdir(parents=True, exist_ok=True)
            
            # Build gallery-dl command
            cmd = [
                "gallery-dl",
                "--dest", str(self.output_dir),
                f"https://www.instagram.com/{username}/"
            ]
            
            # Add count limit if specified
            if count:
                cmd.extend(["--range", f"1-{count}"])
            
            # Add verbose output
            cmd.append("--verbose")
            
            self.logger.info(f"Running gallery-dl for @{username}")
            self.logger.debug(f"Command: {' '.join(cmd)}")
            
            # Run gallery-dl
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode == 0:
                self.logger.info(f"Gallery-dl completed successfully for @{username}")
                
                # Count downloaded files
                downloaded_count = len(list(user_dir.glob("*")))
                
                return {
                    'success': True,
                    'downloaded': downloaded_count,
                    'output': result.stdout,
                    'error': None
                }
            else:
                self.logger.error(f"Gallery-dl failed for @{username}: {result.stderr}")
                return {
                    'success': False,
                    'downloaded': 0,
                    'output': result.stdout,
                    'error': result.stderr
                }
                
        except subprocess.TimeoutExpired:
            self.logger.error(f"Gallery-dl timeout for @{username}")
            return {
                'success': False,
                'downloaded': 0,
                'output': '',
                'error': 'Timeout'
            }
        except Exception as e:
            self.logger.error(f"Gallery-dl error for @{username}: {e}")
            return {
                'success': False,
                'downloaded': 0,
                'output': '',
                'error': str(e)
            }
    
    def download_user_stories(self, username: str) -> Dict[str, Any]:
        """
        Download user stories using gallery-dl
        
        Args:
            username: Instagram username
            
        Returns:
            Dict with download results
        """
        username = username.strip().lstrip('@')
        
        try:
            # Create output directory
            user_dir = self.output_dir / "Instagram" / username / "stories"
            user_dir.mkdir(parents=True, exist_ok=True)
            
            # Build gallery-dl command for stories
            cmd = [
                "gallery-dl",
                "--dest", str(self.output_dir),
                f"https://www.instagram.com/{username}/stories/"
            ]
            
            self.logger.info(f"Running gallery-dl for @{username} stories")
            
            # Run gallery-dl
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=180  # 3 minute timeout
            )
            
            if result.returncode == 0:
                downloaded_count = len(list(user_dir.glob("*")))
                return {
                    'success': True,
                    'downloaded': downloaded_count,
                    'output': result.stdout,
                    'error': None
                }
            else:
                return {
                    'success': False,
                    'downloaded': 0,
                    'output': result.stdout,
                    'error': result.stderr
                }
                
        except Exception as e:
            self.logger.error(f"Gallery-dl stories error for @{username}: {e}")
            return {
                'success': False,
                'downloaded': 0,
                'output': '',
                'error': str(e)
            }
    
    def test_authentication(self) -> bool:
        """Test if authentication is working"""
        try:
            # Test with a simple command
            cmd = ["gallery-dl", "--version"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info("Gallery-dl is available")
                return True
            else:
                self.logger.error("Gallery-dl not available")
                return False
                
        except Exception as e:
            self.logger.error(f"Gallery-dl test failed: {e}")
            return False

if __name__ == "__main__":
    # Test the alternative Instagram downloader
    print("🧪 Testing Instagram Gallery-DL Downloader")
    
    # Initialize downloader
    downloader = InstagramGalleryDL()
    
    # Test authentication
    if downloader.test_authentication():
        print("✅ Gallery-dl is available")
        
        # Test download
        test_user = "nasa"
        print(f"Testing download for @{test_user}")
        
        result = downloader.download_user_posts(test_user, count=5)
        
        if result['success']:
            print(f"✅ Downloaded {result['downloaded']} posts")
        else:
            print(f"❌ Download failed: {result['error']}")
    else:
        print("❌ Gallery-dl not available")
        print("Install with: pip install gallery-dl")
