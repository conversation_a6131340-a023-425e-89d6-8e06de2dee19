"""
VSCO Downloader - Download images and content from VSCO profiles
"""
import os
import logging
import time
import json
import re
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any, Union
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import subprocess
import config

# Simple error classes
class AuthenticationError(Exception):
    pass

class NetworkError(Exception):
    pass

class ContentError(Exception):
    pass

class RateLimitError(Exception):
    pass

# Simple retry decorator
def retry_on_error(max_retries=3, delay=1):
    def decorator(func):
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise
                    time.sleep(delay * (attempt + 1))
            return None
        return wrapper
    return decorator
from file_manager import FileManager


class VSCODownloader:
    """Main class for downloading VSCO content"""
    
    def __init__(self, output_dir: Optional[Path] = None, use_gallery_dl: bool = True):
        """
        Initialize the VSCO downloader
        
        Args:
            output_dir: Output directory for downloads
            use_gallery_dl: Whether to use gallery-dl for downloading (recommended)
        """
        self.output_dir = output_dir or config.DOWNLOADS_DIR
        self.use_gallery_dl = use_gallery_dl
        self.file_manager = FileManager()
        
        # Setup logging
        self._setup_logging()
        
        # Session for requests
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # Rate limiting
        self.request_delay = 2.0  # Seconds between requests
        self.last_request_time = 0
        
        self.logger.info("VSCO downloader initialized")
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_file = config.LOGS_DIR / f"vsco_downloader_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=getattr(logging, config.LOG_LEVEL),
            format=config.LOG_FORMAT,
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def _rate_limit(self):
        """Apply rate limiting between requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.request_delay:
            time.sleep(self.request_delay - time_since_last)
        self.last_request_time = time.time()
    
    def _ensure_user_directories(self, username: str) -> Dict[str, Path]:
        """
        Ensure user-specific directories exist
        
        Args:
            username: VSCO username
            
        Returns:
            Dict[str, Path]: Dictionary with paths for content types
        """
        user_dir = self.output_dir / "VSCO" / username
        
        directories = {
            'images': user_dir / 'images',
            'videos': user_dir / 'videos',
            'metadata': user_dir / 'metadata',
            'profile': user_dir / 'profile'
        }
        
        # Create directories
        for dir_path in directories.values():
            dir_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"Created/verified directories for VSCO user: {username}")
        return directories
    
    def _check_gallery_dl(self) -> bool:
        """Check if gallery-dl is available"""
        try:
            result = subprocess.run(['gallery-dl', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return False
    
    @retry_on_error()
    def download_user_with_gallery_dl(self, username: str, count: Optional[int] = None) -> bool:
        """
        Download user content using gallery-dl
        
        Args:
            username: VSCO username
            count: Maximum number of images to download
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self._check_gallery_dl():
                raise ContentError("gallery-dl not found. Please install it: pip install gallery-dl")
            
            self.logger.info(f"Downloading VSCO user {username} with gallery-dl")
            
            # Ensure directories exist
            user_dirs = self._ensure_user_directories(username)
            
            # Construct VSCO URL
            vsco_url = f"https://vsco.co/{username}"
            
            # Build gallery-dl command
            cmd = [
                'gallery-dl',
                '--dest', str(user_dirs['images']),
                '--write-metadata',
                '--write-info-json'
            ]
            
            # Add count limit if specified
            if count:
                cmd.extend(['--range', f'1-{count}'])
            
            cmd.append(vsco_url)
            
            # Execute gallery-dl
            self.logger.info(f"Running command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                self.logger.info(f"Successfully downloaded VSCO content from {username}")
                return True
            else:
                self.logger.error(f"gallery-dl failed: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to download VSCO user {username} with gallery-dl: {str(e)}")
            return False
    
    @retry_on_error()
    def get_user_info(self, username: str) -> Dict[str, Any]:
        """
        Get user profile information
        
        Args:
            username: VSCO username
            
        Returns:
            Dict containing user information
        """
        try:
            self._rate_limit()
            
            url = f"https://vsco.co/{username}"
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract user information from the page
            user_info = {
                'username': username,
                'user_id': f"vsco_{username}",  # VSCO doesn't expose numeric IDs easily
                'name': username,
                'bio': '',
                'location': '',
                'website': '',
                'image_count': 0,
                'follower_count': 0,
                'following_count': 0,
                'url': url
            }
            
            # Try to extract profile information
            try:
                # Look for JSON data in script tags
                script_tags = soup.find_all('script')
                for script in script_tags:
                    if script.string and 'window.__PRELOADED_STATE__' in script.string:
                        # Extract JSON data
                        json_match = re.search(r'window\.__PRELOADED_STATE__\s*=\s*({.*?});', script.string)
                        if json_match:
                            data = json.loads(json_match.group(1))
                            # Extract user info from the data structure
                            if 'users' in data and 'entities' in data['users']:
                                for user_id, user_data in data['users']['entities'].items():
                                    if user_data.get('username') == username:
                                        user_info.update({
                                            'user_id': user_id,  # Use the actual VSCO user ID
                                            'name': user_data.get('fullName', username),
                                            'bio': user_data.get('description', ''),
                                            'location': user_data.get('location', ''),
                                            'website': user_data.get('externalUrl', ''),
                                            'image_count': user_data.get('mediaCount', 0),
                                            'follower_count': user_data.get('followerCount', 0),
                                            'following_count': user_data.get('followingCount', 0)
                                        })
                                        break
                            break
            except Exception as e:
                self.logger.warning(f"Could not extract detailed profile info: {str(e)}")
            
            return user_info
            
        except Exception as e:
            self.logger.error(f"Failed to get user info for {username}: {str(e)}")
            return {
                'username': username,
                'user_id': f"vsco_{username}",
                'name': username,
                'bio': 'Error loading profile',
                'location': '',
                'website': '',
                'image_count': 0,
                'follower_count': 0,
                'following_count': 0,
                'url': f"https://vsco.co/{username}"
            }
    
    @retry_on_error()
    def download_user_images(self, username: str, count: Optional[int] = None) -> bool:
        """
        Download images from a user's profile
        
        Args:
            username: VSCO username
            count: Maximum number of images to download
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Try gallery-dl first if available
            if self.use_gallery_dl and self._check_gallery_dl():
                return self.download_user_with_gallery_dl(username, count)
            
            # Fallback to manual scraping
            return self._download_user_manual(username, count)
            
        except Exception as e:
            self.logger.error(f"Failed to download images from {username}: {str(e)}")
            return False
    
    def _download_user_manual(self, username: str, count: Optional[int] = None) -> bool:
        """
        Manual download method (fallback)
        
        Args:
            username: VSCO username
            count: Maximum number of images to download
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.info(f"Downloading VSCO user {username} manually")
            
            # Ensure directories exist
            user_dirs = self._ensure_user_directories(username)
            
            self._rate_limit()
            
            url = f"https://vsco.co/{username}"
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract image URLs from the page
            image_urls = []
            
            # Look for image elements
            img_tags = soup.find_all('img')
            for img in img_tags:
                src = img.get('src') or img.get('data-src')
                if src and 'vsco.co' in src and '/images/' in src:
                    # Convert to full URL if needed
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = 'https://vsco.co' + src
                    
                    if src not in image_urls:
                        image_urls.append(src)
            
            # Limit count if specified
            if count:
                image_urls = image_urls[:count]
            
            if not image_urls:
                self.logger.warning(f"No images found for user {username}")
                return False
            
            # Download images
            downloaded_count = 0
            for i, img_url in enumerate(image_urls, 1):
                try:
                    self._rate_limit()
                    
                    # Get image
                    img_response = self.session.get(img_url, timeout=30)
                    img_response.raise_for_status()
                    
                    # Generate filename
                    filename = f"vsco_{username}_{i:04d}.jpg"
                    filepath = user_dirs['images'] / filename
                    
                    # Save image
                    with open(filepath, 'wb') as f:
                        f.write(img_response.content)
                    
                    downloaded_count += 1
                    self.logger.info(f"Downloaded {filename}")
                    
                except Exception as e:
                    self.logger.error(f"Failed to download image {img_url}: {str(e)}")
                    continue
            
            self.logger.info(f"Downloaded {downloaded_count}/{len(image_urls)} images from {username}")
            return downloaded_count > 0
            
        except Exception as e:
            self.logger.error(f"Manual download failed for {username}: {str(e)}")
            return False
    
    def download_from_urls(self, urls: List[str]) -> Dict[str, bool]:
        """
        Download from multiple VSCO URLs
        
        Args:
            urls: List of VSCO URLs
            
        Returns:
            Dict mapping URLs to success status
        """
        results = {}
        
        for url in urls:
            try:
                # Extract username from URL
                username = self._extract_username_from_url(url)
                if username:
                    results[url] = self.download_user_images(username)
                else:
                    results[url] = False
                    
                # Add delay between downloads
                time.sleep(self.request_delay)
            except Exception as e:
                self.logger.error(f"Failed to download {url}: {str(e)}")
                results[url] = False
        
        return results
    
    def _extract_username_from_url(self, url: str) -> Optional[str]:
        """
        Extract username from VSCO URL
        
        Args:
            url: VSCO URL
            
        Returns:
            Username if found, None otherwise
        """
        patterns = [
            r'vsco\.co/([^/\?]+)',
            r'vsco\.co/([^/\?]+)/gallery',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    def get_download_summary(self, username: str) -> Dict[str, Any]:
        """
        Get download summary for a user
        
        Args:
            username: VSCO username
            
        Returns:
            Dict with download statistics
        """
        try:
            user_dirs = self._ensure_user_directories(username)
            
            # Count downloaded files
            image_count = len([f for f in user_dirs['images'].iterdir() 
                             if f.is_file() and f.suffix.lower() in ['.jpg', '.jpeg', '.png', '.webp']])
            
            video_count = len([f for f in user_dirs['videos'].iterdir() 
                             if f.is_file() and f.suffix.lower() in ['.mp4', '.mov', '.avi']])
            
            return {
                'username': username,
                'images_downloaded': image_count,
                'videos_downloaded': video_count,
                'download_path': user_dirs['images'],
                'total_size': sum(f.stat().st_size for f in user_dirs['images'].iterdir() if f.is_file()) +
                             sum(f.stat().st_size for f in user_dirs['videos'].iterdir() if f.is_file())
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get download summary for {username}: {str(e)}")
            return {
                'username': username,
                'images_downloaded': 0,
                'videos_downloaded': 0,
                'download_path': self.output_dir / "VSCO" / username / 'images',
                'total_size': 0
            }
    
    def show_download_summary(self, username: str):
        """
        Show download summary with file locations
        
        Args:
            username: VSCO username
        """
        summary = self.get_download_summary(username)
        
        self.logger.info(f"\n📁 Downloads for VSCO user {username} saved to: {summary['download_path']}")
        self.logger.info(f"  📸 Images: {summary['images_downloaded']} files")
        self.logger.info(f"  🎬 Videos: {summary['videos_downloaded']} files")
        
        if summary['total_size'] > 0:
            size_mb = summary['total_size'] / (1024 * 1024)
            self.logger.info(f"  💾 Total size: {size_mb:.1f} MB")
