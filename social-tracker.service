[Unit]
Description=Social Media Auto Tracker
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/social-tracker
ExecStart=/usr/bin/python3 /home/<USER>/social-tracker/auto_tracker.py daemon --interval 1800
Restart=always
RestartSec=30
StandardOutput=journal
StandardError=journal
SyslogIdentifier=social-tracker

# Environment
Environment=PYTHONPATH=/home/<USER>/social-tracker
Environment=HOME=/home/<USER>

# Security
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=read-only
ReadWritePaths=/home/<USER>/.social_tracker /home/<USER>/Downloads

[Install]
WantedBy=multi-user.target
