{"id": "7525475020471192854", "formats": [{"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "download", "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/okehfOGGRqGvwfWIMtL0fgU5WskgRAGq2tAQmA/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2316&bt=1158&cs=0&ds=3&ft=4KJMyMzm8Zmo0psmPI4jV.AuQpWrKsd.&mime_type=video_mp4&qs=0&rc=ZzhnNzRoNzxpaDgzZzNoPEBpamVqdHQ5cm13NDMzbzczNUAwNjU2MGJgXzUxM2IzNi1jYSNhNC5oMmRzamthLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=202507152356567F9D9350CC2AD32478FF&ply_type=2&policy=2&signature=983d425a7b74b0549b4a8e568a56b203&tk=tt_chain_token", "format_note": "watermarked", "preference": -2, "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "dynamic_range": "SDR", "cookies": "ttwid=1%7COFmoa2Yg02Y4-pnPjxY-CsFpGoYulnPij1-EUH1C4hs%7C1752623816%7Ce6026b7dd651306be1a4be42d153cbed7a75614b7a74c43f47fd43533c01a5e9; Domain=.tiktok.com; Path=/; Expires=**********; tt_csrf_token=YiOh3pTo-nVVGPRpvuEDhAEGVnscQtR5d26Y; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"/iu2Qyljo8cko1rR2PdKZQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=**********", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525475020471192854"}, "format": "download - unknown (watermarked)"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_1171080-0", "tbr": 1171, "quality": 1, "preference": -1, "filesize": 9397924, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oMX0yHehvAadWbfBQVB2tGbGQBf8ifgsRNZAAM/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2286&bt=1143&cs=0&ds=6&ft=4KJMyMzm8Zmo0psmPI4jV.AuQpWrKsd.&mime_type=video_mp4&qs=0&rc=NDRkOzlkPDVnNTloM2k0ZEBpamVqdHQ5cm13NDMzbzczNUBeMDVfYWJeNjAxXjZeL2EwYSNhNC5oMmRzamthLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=202507152356567F9D9350CC2AD32478FF&ply_type=2&policy=2&signature=c29ef69672a94d6d14068262f897bdd9&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7COFmoa2Yg02Y4-pnPjxY-CsFpGoYulnPij1-EUH1C4hs%7C1752623816%7Ce6026b7dd651306be1a4be42d153cbed7a75614b7a74c43f47fd43533c01a5e9; Domain=.tiktok.com; Path=/; Expires=**********; tt_csrf_token=YiOh3pTo-nVVGPRpvuEDhAEGVnscQtR5d26Y; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"/iu2Qyljo8cko1rR2PdKZQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=**********", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525475020471192854"}, "format": "h264_540p_1171080-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_1171080-1", "tbr": 1171, "quality": 1, "preference": -1, "filesize": 9397924, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oMX0yHehvAadWbfBQVB2tGbGQBf8ifgsRNZAAM/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2286&bt=1143&cs=0&ds=6&ft=4KJMyMzm8Zmo0psmPI4jV.AuQpWrKsd.&mime_type=video_mp4&qs=0&rc=NDRkOzlkPDVnNTloM2k0ZEBpamVqdHQ5cm13NDMzbzczNUBeMDVfYWJeNjAxXjZeL2EwYSNhNC5oMmRzamthLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=202507152356567F9D9350CC2AD32478FF&ply_type=2&policy=2&signature=c29ef69672a94d6d14068262f897bdd9&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7COFmoa2Yg02Y4-pnPjxY-CsFpGoYulnPij1-EUH1C4hs%7C1752623816%7Ce6026b7dd651306be1a4be42d153cbed7a75614b7a74c43f47fd43533c01a5e9; Domain=.tiktok.com; Path=/; Expires=**********; tt_csrf_token=YiOh3pTo-nVVGPRpvuEDhAEGVnscQtR5d26Y; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"/iu2Qyljo8cko1rR2PdKZQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=**********", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525475020471192854"}, "format": "h264_540p_1171080-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_409033-0", "tbr": 409, "quality": 1, "preference": -1, "filesize": 3282497, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/ocoXEbkRIE4epVBDFDgirbWFIf3MoniBoQ26vA/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=798&bt=399&cs=2&ds=6&ft=4KJMyMzm8Zmo0psmPI4jV.AuQpWrKsd.&mime_type=video_mp4&qs=11&rc=OTxkO2hlZmg7Z2k4NGU8aEBpamVqdHQ5cm13NDMzbzczNUAuYS00NGEtX2AxXl4wLV5hYSNhNC5oMmRzamthLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=202507152356567F9D9350CC2AD32478FF&ply_type=2&policy=2&signature=08b753a09c5782684ecfb94259730225&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7COFmoa2Yg02Y4-pnPjxY-CsFpGoYulnPij1-EUH1C4hs%7C1752623816%7Ce6026b7dd651306be1a4be42d153cbed7a75614b7a74c43f47fd43533c01a5e9; Domain=.tiktok.com; Path=/; Expires=**********; tt_csrf_token=YiOh3pTo-nVVGPRpvuEDhAEGVnscQtR5d26Y; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"/iu2Qyljo8cko1rR2PdKZQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=**********", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525475020471192854"}, "format": "bytevc1_540p_409033-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_409033-1", "tbr": 409, "quality": 1, "preference": -1, "filesize": 3282497, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/ocoXEbkRIE4epVBDFDgirbWFIf3MoniBoQ26vA/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=798&bt=399&cs=2&ds=6&ft=4KJMyMzm8Zmo0psmPI4jV.AuQpWrKsd.&mime_type=video_mp4&qs=11&rc=OTxkO2hlZmg7Z2k4NGU8aEBpamVqdHQ5cm13NDMzbzczNUAuYS00NGEtX2AxXl4wLV5hYSNhNC5oMmRzamthLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=202507152356567F9D9350CC2AD32478FF&ply_type=2&policy=2&signature=08b753a09c5782684ecfb94259730225&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7COFmoa2Yg02Y4-pnPjxY-CsFpGoYulnPij1-EUH1C4hs%7C1752623816%7Ce6026b7dd651306be1a4be42d153cbed7a75614b7a74c43f47fd43533c01a5e9; Domain=.tiktok.com; Path=/; Expires=**********; tt_csrf_token=YiOh3pTo-nVVGPRpvuEDhAEGVnscQtR5d26Y; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"/iu2Qyljo8cko1rR2PdKZQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=**********", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525475020471192854"}, "format": "bytevc1_540p_409033-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_510875-0", "tbr": 510, "quality": 2, "preference": -1, "filesize": 4099776, "width": 720, "height": 1280, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/ogcEFbnk4EAerDWoDN32vfQ6bgi6pBBNoIMR6i/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=996&bt=498&cs=2&ds=3&ft=4KJMyMzm8Zmo0psmPI4jV.AuQpWrKsd.&mime_type=video_mp4&qs=14&rc=ZDozZmg6NDVpOjZpNzNpNkBpamVqdHQ5cm13NDMzbzczNUAyMTFeMGFiNl8xXjIxNS0zYSNhNC5oMmRzamthLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=202507152356567F9D9350CC2AD32478FF&ply_type=2&policy=2&signature=20b43a7ebaa513bd60b145ea85dbb129&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7COFmoa2Yg02Y4-pnPjxY-CsFpGoYulnPij1-EUH1C4hs%7C1752623816%7Ce6026b7dd651306be1a4be42d153cbed7a75614b7a74c43f47fd43533c01a5e9; Domain=.tiktok.com; Path=/; Expires=**********; tt_csrf_token=YiOh3pTo-nVVGPRpvuEDhAEGVnscQtR5d26Y; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"/iu2Qyljo8cko1rR2PdKZQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=**********", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525475020471192854"}, "format": "bytevc1_720p_510875-0 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_510875-1", "tbr": 510, "quality": 2, "preference": -1, "filesize": 4099776, "width": 720, "height": 1280, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/ogcEFbnk4EAerDWoDN32vfQ6bgi6pBBNoIMR6i/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=996&bt=498&cs=2&ds=3&ft=4KJMyMzm8Zmo0psmPI4jV.AuQpWrKsd.&mime_type=video_mp4&qs=14&rc=ZDozZmg6NDVpOjZpNzNpNkBpamVqdHQ5cm13NDMzbzczNUAyMTFeMGFiNl8xXjIxNS0zYSNhNC5oMmRzamthLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=202507152356567F9D9350CC2AD32478FF&ply_type=2&policy=2&signature=20b43a7ebaa513bd60b145ea85dbb129&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7COFmoa2Yg02Y4-pnPjxY-CsFpGoYulnPij1-EUH1C4hs%7C1752623816%7Ce6026b7dd651306be1a4be42d153cbed7a75614b7a74c43f47fd43533c01a5e9; Domain=.tiktok.com; Path=/; Expires=**********; tt_csrf_token=YiOh3pTo-nVVGPRpvuEDhAEGVnscQtR5d26Y; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"/iu2Qyljo8cko1rR2PdKZQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=**********", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525475020471192854"}, "format": "bytevc1_720p_510875-1 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_899985-0", "tbr": 899, "quality": 3, "preference": -1, "filesize": 7220247, "width": 1080, "height": 1920, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oMobt6r3E4ebBDpEInGvDFA5QRiiRkfoDgMB2i/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1756&bt=878&cs=2&ds=4&ft=4KJMyMzm8Zmo0psmPI4jV.AuQpWrKsd.&mime_type=video_mp4&qs=15&rc=NmQ6OzM3Mzc6ZGRoNzxpZEBpamVqdHQ5cm13NDMzbzczNUAuYzAxLTQwXy0xYjE2LjMvYSNhNC5oMmRzamthLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=202507152356567F9D9350CC2AD32478FF&ply_type=2&policy=2&signature=0b92d9d0605365352dce00b4549bc1a5&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7COFmoa2Yg02Y4-pnPjxY-CsFpGoYulnPij1-EUH1C4hs%7C1752623816%7Ce6026b7dd651306be1a4be42d153cbed7a75614b7a74c43f47fd43533c01a5e9; Domain=.tiktok.com; Path=/; Expires=**********; tt_csrf_token=YiOh3pTo-nVVGPRpvuEDhAEGVnscQtR5d26Y; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"/iu2Qyljo8cko1rR2PdKZQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=**********", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525475020471192854"}, "format": "bytevc1_1080p_899985-0 - 1080x1920"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_899985-1", "tbr": 899, "quality": 3, "preference": -1, "filesize": 7220247, "width": 1080, "height": 1920, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oMobt6r3E4ebBDpEInGvDFA5QRiiRkfoDgMB2i/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1756&bt=878&cs=2&ds=4&ft=4KJMyMzm8Zmo0psmPI4jV.AuQpWrKsd.&mime_type=video_mp4&qs=15&rc=NmQ6OzM3Mzc6ZGRoNzxpZEBpamVqdHQ5cm13NDMzbzczNUAuYzAxLTQwXy0xYjE2LjMvYSNhNC5oMmRzamthLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=202507152356567F9D9350CC2AD32478FF&ply_type=2&policy=2&signature=0b92d9d0605365352dce00b4549bc1a5&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7COFmoa2Yg02Y4-pnPjxY-CsFpGoYulnPij1-EUH1C4hs%7C1752623816%7Ce6026b7dd651306be1a4be42d153cbed7a75614b7a74c43f47fd43533c01a5e9; Domain=.tiktok.com; Path=/; Expires=**********; tt_csrf_token=YiOh3pTo-nVVGPRpvuEDhAEGVnscQtR5d26Y; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"/iu2Qyljo8cko1rR2PdKZQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=**********", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525475020471192854"}, "format": "bytevc1_1080p_899985-1 - 1080x1920"}], "subtitles": {}, "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7525475020471192854"}, "channel": "<PERSON>", "channel_id": "MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader": "dylan.page", "uploader_id": "6760368158794155013", "channel_url": "https://www.tiktok.com/@MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader_url": "https://www.tiktok.com/@dylan.page", "track": "original sound", "artists": ["<PERSON>"], "duration": 64, "title": "USA gunna have to entertain themselves 😭🙏🥀", "description": "USA gunna have to entertain themselves 😭🙏🥀", "timestamp": 1752161197, "view_count": 2700000, "like_count": 278800, "repost_count": 11800, "comment_count": 5824, "thumbnails": [{"id": "dynamicCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oIFkjAGt3G7edAK1fmeOIkZIAULLQIQ0LgAA11~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=Uc9jJpMyCeBNC317M3XNkFLUuHQ%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -2}, {"id": "cover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oIFkjAGt3G7edAK1fmeOIkZIAULLQIQ0LgAA11~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=Uc9jJpMyCeBNC317M3XNkFLUuHQ%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}, {"id": "originCover", "url": "https://p19-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oApD8dBnIiA2IrBbbBikG2EUF6etfRMvoo6dgr~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=YnGfKKQToRcocr%2BnIovCcnQm6co%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}], "webpage_url": "https://www.tiktok.com/@dylan.page/video/7525475020471192854", "webpage_url_basename": "7525475020471192854", "webpage_url_domain": "tiktok.com", "extractor": "TikTok", "extractor_key": "TikTok", "thumbnail": "https://p19-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oApD8dBnIiA2IrBbbBikG2EUF6etfRMvoo6dgr~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=YnGfKKQToRcocr%2BnIovCcnQm6co%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "display_id": "7525475020471192854", "fulltitle": "USA gunna have to entertain themselves 😭🙏🥀", "duration_string": "1:04", "upload_date": "20250710", "artist": "<PERSON>", "epoch": 1752623816, "ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_409033-1", "tbr": 409, "quality": 1, "preference": -1, "filesize": 3282497, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/ocoXEbkRIE4epVBDFDgirbWFIf3MoniBoQ26vA/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=798&bt=399&cs=2&ds=6&ft=4KJMyMzm8Zmo0psmPI4jV.AuQpWrKsd.&mime_type=video_mp4&qs=11&rc=OTxkO2hlZmg7Z2k4NGU8aEBpamVqdHQ5cm13NDMzbzczNUAuYS00NGEtX2AxXl4wLV5hYSNhNC5oMmRzamthLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=202507152356567F9D9350CC2AD32478FF&ply_type=2&policy=2&signature=08b753a09c5782684ecfb94259730225&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7COFmoa2Yg02Y4-pnPjxY-CsFpGoYulnPij1-EUH1C4hs%7C1752623816%7Ce6026b7dd651306be1a4be42d153cbed7a75614b7a74c43f47fd43533c01a5e9; Domain=.tiktok.com; Path=/; Expires=**********; tt_csrf_token=YiOh3pTo-nVVGPRpvuEDhAEGVnscQtR5d26Y; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"/iu2Qyljo8cko1rR2PdKZQ==\"; Domain=.tiktok.com; Path=/; Secure; Expires=**********", "format": "bytevc1_540p_409033-1 - 576x1024", "_type": "video", "_version": {"version": "2025.06.30", "release_git_head": "b0187844988e557c7e1e6bb1aabd4c1176768d86", "repository": "yt-dlp/yt-dlp"}}