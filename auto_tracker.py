#!/usr/bin/env python3
"""
Automatic Social Media Tracker - Ubuntu Server Compatible
Streamlined system for Instagram, TikTok, and VSCO automatic tracking
"""
import os
import sys
import json
import sqlite3
import logging
import time
import random
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Set, Any
import signal
import argparse

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

import config


class AutoTracker:
    """Automatic social media content tracker for Ubuntu servers"""
    
    def __init__(self, db_path: Optional[Path] = None, downloads_dir: Optional[Path] = None):
        """
        Initialize auto tracker
        
        Args:
            db_path: Path to SQLite database
            downloads_dir: Path to downloads directory
        """
        self.db_path = db_path or Path.home() / ".social_tracker" / "tracker.db"
        self.downloads_dir = downloads_dir or Path.home() / "Downloads" / "SocialMedia"
        self.config_dir = self.db_path.parent
        
        # Ensure directories exist
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.downloads_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup logging
        self.setup_logging()
        
        # Initialize database
        self.init_database()
        
        # Tracking state
        self.running = False
        self.check_interval = config.DEFAULT_CHECK_INTERVAL  # Use config default (30 minutes)
        self.platforms = ['instagram', 'tiktok', 'vsco']
        
        # Platform downloaders
        self.downloaders = {}
        self.init_downloaders()
    
    def setup_logging(self):
        """Setup logging for server environment"""
        log_file = self.config_dir / "tracker.log"
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger('AutoTracker')
        self.logger.info(f"Auto tracker initialized - DB: {self.db_path}")
    
    def init_database(self):
        """Initialize SQLite database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Tracked users table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS tracked_users (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        platform TEXT NOT NULL,
                        username TEXT NOT NULL,
                        display_name TEXT,
                        enabled BOOLEAN DEFAULT 1,
                        check_posts BOOLEAN DEFAULT 1,
                        check_stories BOOLEAN DEFAULT 1,
                        check_reels BOOLEAN DEFAULT 1,
                        last_check TIMESTAMP,
                        last_download TIMESTAMP,
                        total_downloads INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(platform, username)
                    )
                ''')
                
                # Downloaded content tracking
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS downloaded_content (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        platform TEXT NOT NULL,
                        username TEXT NOT NULL,
                        content_type TEXT NOT NULL,
                        content_id TEXT NOT NULL,
                        content_url TEXT,
                        file_path TEXT,
                        download_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        post_date TIMESTAMP,
                        metadata TEXT,
                        FOREIGN KEY (user_id) REFERENCES tracked_users (id),
                        UNIQUE(platform, username, content_type, content_id)
                    )
                ''')
                
                # Check sessions
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS check_sessions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        platform TEXT NOT NULL,
                        username TEXT NOT NULL,
                        session_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        session_end TIMESTAMP,
                        items_found INTEGER DEFAULT 0,
                        items_downloaded INTEGER DEFAULT 0,
                        items_skipped INTEGER DEFAULT 0,
                        errors INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'running',
                        error_message TEXT,
                        FOREIGN KEY (user_id) REFERENCES tracked_users (id)
                    )
                ''')
                
                # Create indexes
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_tracked_users_platform ON tracked_users(platform, username)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_downloaded_content_lookup ON downloaded_content(platform, username, content_type, content_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_check_sessions_user ON check_sessions(user_id)')
                
                conn.commit()
                self.logger.info("Database initialized successfully")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize database: {e}")
            raise
    
    def init_downloaders(self):
        """Initialize platform downloaders"""
        try:
            # Instagram
            try:
                from instagram_downloader import InstagramDownloader
                # Auto-detect session file from ~/.social_tracker/sessions/
                session_dir = Path.home() / ".social_tracker" / "sessions"
                session_file = None

                if session_dir.exists():
                    # Find any session file in the directory
                    session_files = list(session_dir.glob("*_session"))
                    if session_files:
                        session_file = str(session_files[0])  # Use the first session file found
                        self.logger.info(f"Auto-detected Instagram session: {session_file}")

                # Fallback to config if no session file found
                if not session_file:
                    session_file = getattr(config, 'INSTAGRAM_SESSION_FILE', None)
                    username = getattr(config, 'INSTAGRAM_USERNAME', None)
                    password = getattr(config, 'INSTAGRAM_PASSWORD', None)
                else:
                    username = None
                    password = None

                self.downloaders['instagram'] = InstagramDownloader(
                    username=username,
                    password=password,
                    session_file=session_file
                )
                self.logger.info("Instagram downloader initialized")
            except Exception as e:
                self.logger.warning(f"Instagram downloader failed: {e}")
            
            # TikTok
            try:
                from tiktok_downloader import TikTokDownloader
                self.downloaders['tiktok'] = TikTokDownloader()
                self.logger.info("TikTok downloader initialized")
            except Exception as e:
                self.logger.warning(f"TikTok downloader failed: {e}")
            
            # VSCO
            try:
                from vsco_downloader import VSCODownloader
                self.downloaders['vsco'] = VSCODownloader()
                self.logger.info("VSCO downloader initialized")
            except Exception as e:
                self.logger.warning(f"VSCO downloader failed: {e}")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize downloaders: {e}")
    
    def add_user(self, platform: str, username: str, **options) -> bool:
        """Add a user to tracking"""
        if platform not in self.platforms:
            self.logger.error(f"Unsupported platform: {platform}")
            return False
        
        # Clean username
        username = username.strip().lstrip('@')
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Use username as display name for now (skip user info lookup to avoid hanging)
                display_name = username
                
                # Insert or update user
                cursor.execute('''
                    INSERT OR REPLACE INTO tracked_users 
                    (platform, username, display_name, enabled, check_posts, check_stories, check_reels, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    platform, username, display_name,
                    options.get('enabled', True),
                    options.get('check_posts', True),
                    options.get('check_stories', True),
                    options.get('check_reels', True),
                    datetime.now()
                ))
                
                conn.commit()
                self.logger.info(f"Added user: {platform} @{username}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to add user {platform} @{username}: {e}")
            return False
    
    def remove_user(self, platform: str, username: str) -> bool:
        """Remove a user from tracking"""
        username = username.strip().lstrip('@')
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get user ID
                cursor.execute('SELECT id FROM tracked_users WHERE platform = ? AND username = ?', 
                             (platform, username))
                result = cursor.fetchone()
                
                if not result:
                    self.logger.warning(f"User not found: {platform} @{username}")
                    return False
                
                user_id = result[0]
                
                # Delete related records
                cursor.execute('DELETE FROM check_sessions WHERE user_id = ?', (user_id,))
                cursor.execute('DELETE FROM downloaded_content WHERE user_id = ?', (user_id,))
                cursor.execute('DELETE FROM tracked_users WHERE id = ?', (user_id,))
                
                conn.commit()
                self.logger.info(f"Removed user: {platform} @{username}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to remove user {platform} @{username}: {e}")
            return False
    
    def list_users(self, platform: str = None) -> List[Dict[str, Any]]:
        """List tracked users"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if platform:
                    cursor.execute('''
                        SELECT platform, username, display_name, enabled, last_check, total_downloads
                        FROM tracked_users 
                        WHERE platform = ? 
                        ORDER BY username
                    ''', (platform,))
                else:
                    cursor.execute('''
                        SELECT platform, username, display_name, enabled, last_check, total_downloads
                        FROM tracked_users 
                        ORDER BY platform, username
                    ''')
                
                columns = ['platform', 'username', 'display_name', 'enabled', 'last_check', 'total_downloads']
                users = []
                
                for row in cursor.fetchall():
                    user = dict(zip(columns, row))
                    users.append(user)
                
                return users
                
        except Exception as e:
            self.logger.error(f"Failed to list users: {e}")
            return []
    
    def get_downloaded_content_ids(self, platform: str, username: str, content_type: str = None) -> Set[str]:
        """Get set of already downloaded content IDs"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if content_type:
                    cursor.execute('''
                        SELECT content_id FROM downloaded_content 
                        WHERE platform = ? AND username = ? AND content_type = ?
                    ''', (platform, username, content_type))
                else:
                    cursor.execute('''
                        SELECT content_id FROM downloaded_content 
                        WHERE platform = ? AND username = ?
                    ''', (platform, username))
                
                return {row[0] for row in cursor.fetchall()}
                
        except Exception as e:
            self.logger.error(f"Failed to get downloaded content IDs: {e}")
            return set()
    
    def record_downloaded_content(self, user_id: int, platform: str, username: str, 
                                content_type: str, content_id: str, content_info: Dict[str, Any]) -> bool:
        """Record downloaded content"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO downloaded_content
                    (user_id, platform, username, content_type, content_id, content_url,
                     file_path, post_date, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    user_id, platform, username, content_type, content_id,
                    content_info.get('url'), content_info.get('file_path'),
                    content_info.get('post_date'), json.dumps(content_info.get('metadata', {}))
                ))
                
                # Update user download count
                cursor.execute('''
                    UPDATE tracked_users 
                    SET total_downloads = total_downloads + 1, last_download = ?
                    WHERE id = ?
                ''', (datetime.now(), user_id))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to record downloaded content: {e}")
            return False
    
    def start_check_session(self, user_id: int, platform: str, username: str) -> int:
        """Start a new check session"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO check_sessions
                    (user_id, platform, username)
                    VALUES (?, ?, ?)
                ''', (user_id, platform, username))
                
                session_id = cursor.lastrowid
                conn.commit()
                return session_id
                
        except Exception as e:
            self.logger.error(f"Failed to start check session: {e}")
            return None
    
    def end_check_session(self, session_id: int, stats: Dict[str, Any]):
        """End a check session with statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE check_sessions 
                    SET session_end = ?, items_found = ?, items_downloaded = ?, 
                        items_skipped = ?, errors = ?, status = ?, error_message = ?
                    WHERE id = ?
                ''', (
                    datetime.now(), stats.get('found', 0), stats.get('downloaded', 0),
                    stats.get('skipped', 0), stats.get('errors', 0), 
                    stats.get('status', 'completed'), stats.get('error_message'),
                    session_id
                ))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Failed to end check session: {e}")
    
    def update_last_check(self, user_id: int):
        """Update last check timestamp"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE tracked_users 
                    SET last_check = ?, updated_at = ?
                    WHERE id = ?
                ''', (datetime.now(), datetime.now(), user_id))
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Failed to update last check: {e}")

    def check_user_for_new_content(self, platform: str, username: str) -> Dict[str, Any]:
        """Check a single user for new content"""
        username = username.strip().lstrip('@')

        try:
            # Get user info
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, enabled, check_posts, check_stories, check_reels
                    FROM tracked_users
                    WHERE platform = ? AND username = ?
                ''', (platform, username))

                result = cursor.fetchone()
                if not result:
                    return {'error': 'User not found in tracking'}

                user_id, enabled, check_posts, check_stories, check_reels = result

                if not enabled:
                    return {'error': 'User tracking disabled'}

            # Start check session
            session_id = self.start_check_session(user_id, platform, username)

            # Get downloader
            if platform not in self.downloaders:
                error_msg = f"No downloader available for {platform}"
                self.end_check_session(session_id, {'status': 'failed', 'error_message': error_msg})
                return {'error': error_msg}

            downloader = self.downloaders[platform]

            # Get already downloaded content
            downloaded_posts = self.get_downloaded_content_ids(platform, username, 'posts') if check_posts else set()
            downloaded_stories = self.get_downloaded_content_ids(platform, username, 'stories') if check_stories else set()
            downloaded_reels = self.get_downloaded_content_ids(platform, username, 'reels') if check_reels else set()

            # Check for new content based on platform
            new_content = {}
            total_found = 0
            total_downloaded = 0
            total_errors = 0

            if platform == 'instagram':
                result = self._check_instagram_content(downloader, username, user_id, session_id,
                                                     downloaded_posts, downloaded_stories, downloaded_reels,
                                                     check_posts, check_stories, check_reels)
            elif platform == 'tiktok':
                result = self._check_tiktok_content(downloader, username, user_id, session_id,
                                                  downloaded_posts, check_posts)
            elif platform == 'vsco':
                result = self._check_vsco_content(downloader, username, user_id, session_id,
                                                downloaded_posts, check_posts)
            else:
                result = {'found': 0, 'downloaded': 0, 'errors': 1, 'error_message': 'Unsupported platform'}

            # End session
            self.end_check_session(session_id, {
                'status': 'completed' if result['errors'] == 0 else 'partial',
                'found': result['found'],
                'downloaded': result['downloaded'],
                'skipped': result['found'] - result['downloaded'],
                'errors': result['errors'],
                'error_message': result.get('error_message')
            })

            # Update last check
            self.update_last_check(user_id)

            return {
                'platform': platform,
                'username': username,
                'found': result['found'],
                'downloaded': result['downloaded'],
                'skipped': result['found'] - result['downloaded'],
                'errors': result['errors']
            }

        except Exception as e:
            self.logger.error(f"Failed to check user {platform} @{username}: {e}")
            return {'error': str(e)}

    def _check_instagram_content(self, downloader, username: str, user_id: int, session_id: int,
                               downloaded_posts: Set[str], downloaded_stories: Set[str],
                               downloaded_reels: Set[str], check_posts: bool, check_stories: bool,
                               check_reels: bool) -> Dict[str, Any]:
        """Check Instagram for new content"""
        found = 0
        downloaded = 0
        errors = 0

        try:
            from instaloader import Profile

            profile = Profile.from_username(downloader.loader.context, username)

            # Check posts
            if check_posts:
                try:
                    post_count = 0
                    for post in profile.get_posts():
                        if post_count >= 300:  # Limit to recent posts
                            break

                        post_id = post.shortcode
                        found += 1

                        if post_id not in downloaded_posts:
                            # Download post
                            try:
                                success = downloader.download_post_by_shortcode(post_id)
                                if success:
                                    # Record download
                                    self.record_downloaded_content(user_id, 'instagram', username, 'posts', post_id, {
                                        'url': f"https://www.instagram.com/p/{post_id}/",
                                        'post_date': post.date,
                                        'caption': post.caption,
                                        'metadata': {'likes': post.likes, 'comments': post.comments}
                                    })
                                    downloaded += 1
                                    self.logger.info(f"Downloaded Instagram post: @{username} - {post_id}")
                                else:
                                    errors += 1
                            except Exception as e:
                                self.logger.error(f"Failed to download Instagram post {post_id}: {e}")
                                errors += 1

                        post_count += 1

                except Exception as e:
                    error_msg = str(e)
                    if "401 Unauthorized" in error_msg or "Please wait" in error_msg:
                        self.logger.warning(f"Instagram rate limiting for @{username} - skipping Instagram checks")
                        # Return early when rate limited to avoid further Instagram API calls
                        return {'found': found, 'downloaded': downloaded, 'errors': 0, 'rate_limited': True}
                    else:
                        self.logger.error(f"Failed to check Instagram posts for @{username}: {e}")
                        errors += 1

            # Add delay between content type checks to avoid rate limiting
            if check_posts and (check_stories or check_reels):
                delay = random.uniform(60, 120)  # 1-2 minutes between content types
                self.logger.debug(f"Waiting {delay:.1f} seconds between Instagram content checks...")
                time.sleep(delay)

            # Check stories
            if check_stories:
                try:
                    for story in downloader.loader.get_stories([profile.userid]):
                        for item in story.get_items():
                            story_id = f"{item.mediaid}"
                            found += 1

                            if story_id not in downloaded_stories:
                                # Download story
                                try:
                                    success = downloader.download_story_item(item)
                                    if success:
                                        self.record_downloaded_content(user_id, 'instagram', username, 'stories', story_id, {
                                            'post_date': item.date,
                                            'metadata': {'is_video': item.is_video}
                                        })
                                        downloaded += 1
                                        self.logger.info(f"Downloaded Instagram story: @{username} - {story_id}")
                                    else:
                                        errors += 1
                                except Exception as e:
                                    self.logger.error(f"Failed to download Instagram story {story_id}: {e}")
                                    errors += 1

                except Exception as e:
                    self.logger.error(f"Failed to check Instagram stories for @{username}: {e}")
                    errors += 1

            # Add delay between stories and reels check
            if check_stories and check_reels:
                delay = random.uniform(60, 120)  # 1-2 minutes between content types
                self.logger.debug(f"Waiting {delay:.1f} seconds between Instagram content checks...")
                time.sleep(delay)

            # Check reels
            if check_reels:
                try:
                    reel_count = 0
                    for post in profile.get_posts():
                        if reel_count >= 300:  # Limit reels check
                            break

                        if post.typename == 'GraphVideo' and hasattr(post, 'is_video') and post.is_video:
                            reel_id = post.shortcode
                            found += 1

                            if reel_id not in downloaded_reels:
                                # Download reel
                                try:
                                    success = downloader.download_post_by_shortcode(reel_id)
                                    if success:
                                        self.record_downloaded_content(user_id, 'instagram', username, 'reels', reel_id, {
                                            'url': f"https://www.instagram.com/reel/{reel_id}/",
                                            'post_date': post.date,
                                            'caption': post.caption,
                                            'metadata': {'likes': post.likes, 'comments': post.comments}
                                        })
                                        downloaded += 1
                                        self.logger.info(f"Downloaded Instagram reel: @{username} - {reel_id}")
                                    else:
                                        errors += 1
                                except Exception as e:
                                    self.logger.error(f"Failed to download Instagram reel {reel_id}: {e}")
                                    errors += 1

                        reel_count += 1

                except Exception as e:
                    self.logger.error(f"Failed to check Instagram reels for @{username}: {e}")
                    errors += 1

        except Exception as e:
            self.logger.error(f"Failed to check Instagram content for @{username}: {e}")
            errors += 1

        return {'found': found, 'downloaded': downloaded, 'errors': errors}

    def _check_tiktok_content(self, downloader, username: str, user_id: int, session_id: int,
                            downloaded_videos: Set[str], check_videos: bool) -> Dict[str, Any]:
        """Check TikTok for new content"""
        found = 0
        downloaded = 0
        errors = 0

        if not check_videos:
            return {'found': found, 'downloaded': downloaded, 'errors': errors}

        try:
            # Use yt-dlp to get video list
            import yt_dlp

            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'extract_flat': True,
                'playlistend': 300  # Check recent 300 videos
            }

            user_url = f"https://www.tiktok.com/@{username}"

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                try:
                    info = ydl.extract_info(user_url, download=False)
                    if info and 'entries' in info:
                        for entry in info['entries']:
                            video_id = entry.get('id')
                            if not video_id:
                                continue

                            found += 1

                            if video_id not in downloaded_videos:
                                # Download video
                                try:
                                    video_url = entry.get('url') or f"https://www.tiktok.com/@{username}/video/{video_id}"
                                    success = downloader.download_video(video_url)
                                    if success:
                                        self.record_downloaded_content(user_id, 'tiktok', username, 'videos', video_id, {
                                            'url': video_url,
                                            'title': entry.get('title', ''),
                                            'metadata': {'duration': entry.get('duration'), 'upload_date': entry.get('upload_date')}
                                        })
                                        downloaded += 1
                                        self.logger.info(f"Downloaded TikTok video: @{username} - {video_id}")
                                    else:
                                        errors += 1
                                except Exception as e:
                                    self.logger.error(f"Failed to download TikTok video {video_id}: {e}")
                                    errors += 1

                except Exception as e:
                    self.logger.error(f"Failed to extract TikTok videos for @{username}: {e}")
                    errors += 1

        except Exception as e:
            self.logger.error(f"Failed to check TikTok content for @{username}: {e}")
            errors += 1

        return {'found': found, 'downloaded': downloaded, 'errors': errors}

    def _check_vsco_content(self, downloader, username: str, user_id: int, session_id: int,
                          downloaded_images: Set[str], check_images: bool) -> Dict[str, Any]:
        """Check VSCO for new content"""
        found = 0
        downloaded = 0
        errors = 0

        if not check_images:
            return {'found': found, 'downloaded': downloaded, 'errors': errors}

        try:
            # Use gallery-dl to get image list
            import subprocess
            import tempfile
            import json

            # Create temp file for gallery-dl output
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.json', delete=False) as f:
                temp_file = f.name

            try:
                # Run gallery-dl to extract URLs
                cmd = [
                    'gallery-dl',
                    '--extract',
                    '--write-info-json',
                    '--output', temp_file,
                    f'https://vsco.co/{username}'
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

                if result.returncode == 0:
                    # Read extracted info
                    try:
                        with open(temp_file, 'r') as f:
                            for line in f:
                                if line.strip():
                                    try:
                                        info = json.loads(line)
                                        image_id = info.get('id') or info.get('filename', '').split('.')[0]
                                        if not image_id:
                                            continue

                                        found += 1

                                        if image_id not in downloaded_images:
                                            # Download image
                                            try:
                                                image_url = info.get('url')
                                                if image_url:
                                                    success = downloader.download_image_by_url(image_url)
                                                    if success:
                                                        self.record_downloaded_content(user_id, 'vsco', username, 'images', image_id, {
                                                            'url': image_url,
                                                            'metadata': info
                                                        })
                                                        downloaded += 1
                                                        self.logger.info(f"Downloaded VSCO image: @{username} - {image_id}")
                                                    else:
                                                        errors += 1
                                            except Exception as e:
                                                self.logger.error(f"Failed to download VSCO image {image_id}: {e}")
                                                errors += 1

                                    except json.JSONDecodeError:
                                        continue

                    except Exception as e:
                        self.logger.error(f"Failed to read VSCO extraction results: {e}")
                        errors += 1

                else:
                    self.logger.error(f"gallery-dl failed for VSCO @{username}: {result.stderr}")
                    errors += 1

            finally:
                # Clean up temp file
                try:
                    os.unlink(temp_file)
                except:
                    pass

        except Exception as e:
            self.logger.error(f"Failed to check VSCO content for @{username}: {e}")
            errors += 1

        return {'found': found, 'downloaded': downloaded, 'errors': errors}

    def check_all_users(self) -> Dict[str, Any]:
        """Check all enabled users for new content"""
        users = self.list_users()
        enabled_users = [u for u in users if u['enabled']]

        if not enabled_users:
            self.logger.info("No enabled users to check")
            return {'total_users': 0, 'results': {}}

        results = {}
        total_found = 0
        total_downloaded = 0
        total_errors = 0

        self.logger.info(f"Checking {len(enabled_users)} users for new content")

        for user in enabled_users:
            platform = user['platform']
            username = user['username']

            self.logger.info(f"Checking {platform} @{username}")

            try:
                result = self.check_user_for_new_content(platform, username)

                if 'error' not in result:
                    total_found += result.get('found', 0)
                    total_downloaded += result.get('downloaded', 0)
                    total_errors += result.get('errors', 0)

                    if result.get('downloaded', 0) > 0:
                        self.logger.info(f"Downloaded {result['downloaded']} new items from {platform} @{username}")

                results[f"{platform}:{username}"] = result

                # Add delay between users to be respectful - longer for Instagram
                if platform == 'instagram':
                    delay = random.uniform(120, 300)  # 2-5 minutes between Instagram users
                    self.logger.debug(f"Waiting {delay:.1f} seconds before next Instagram user...")
                    time.sleep(delay)
                else:
                    delay = random.uniform(5, 15)   # 5-15 seconds for other platforms
                    time.sleep(delay)

            except Exception as e:
                self.logger.error(f"Failed to check {platform} @{username}: {e}")
                results[f"{platform}:{username}"] = {'error': str(e)}
                total_errors += 1

        summary = {
            'total_users': len(enabled_users),
            'total_found': total_found,
            'total_downloaded': total_downloaded,
            'total_errors': total_errors,
            'results': results
        }

        self.logger.info(f"Check completed: {total_downloaded} downloads, {total_errors} errors")
        return summary

    def start_daemon(self, interval: int = None):
        """Start the automatic checking daemon"""
        if interval:
            self.check_interval = interval

        self.running = True
        self.logger.info(f"Starting auto tracker daemon (interval: {self.check_interval}s)")

        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        try:
            while self.running:
                try:
                    self.logger.info("Starting automatic check cycle")
                    start_time = time.time()

                    # Check all users
                    results = self.check_all_users()

                    # Log summary
                    elapsed = time.time() - start_time
                    self.logger.info(f"Check cycle completed in {elapsed:.1f}s - "
                                   f"Downloaded: {results['total_downloaded']}, "
                                   f"Errors: {results['total_errors']}")

                    # Wait for next cycle
                    if self.running:
                        self.logger.info(f"Waiting {self.check_interval}s until next check")
                        for _ in range(self.check_interval):
                            if not self.running:
                                break
                            time.sleep(1)

                except Exception as e:
                    self.logger.error(f"Error in daemon loop: {e}")
                    if self.running:
                        self.logger.info("Waiting 60s before retry")
                        time.sleep(60)

        except KeyboardInterrupt:
            self.logger.info("Daemon stopped by user")
        finally:
            self.running = False
            self.logger.info("Auto tracker daemon stopped")

    def stop_daemon(self):
        """Stop the daemon"""
        self.running = False
        self.logger.info("Daemon stop requested")

    def _signal_handler(self, signum, frame):
        """Handle system signals"""
        self.logger.info(f"Received signal {signum}, stopping daemon")
        self.stop_daemon()

    def get_stats(self) -> Dict[str, Any]:
        """Get tracker statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # User counts by platform
                cursor.execute('''
                    SELECT platform, COUNT(*) as count, SUM(enabled) as enabled_count
                    FROM tracked_users
                    GROUP BY platform
                ''')
                platform_stats = {}
                for platform, total, enabled in cursor.fetchall():
                    platform_stats[platform] = {'total': total, 'enabled': enabled}

                # Total downloads
                cursor.execute('SELECT COUNT(*) FROM downloaded_content')
                total_downloads = cursor.fetchone()[0]

                # Recent activity
                cursor.execute('''
                    SELECT COUNT(*) FROM downloaded_content
                    WHERE download_date > datetime('now', '-24 hours')
                ''')
                downloads_24h = cursor.fetchone()[0]

                cursor.execute('''
                    SELECT COUNT(*) FROM check_sessions
                    WHERE session_start > datetime('now', '-24 hours')
                ''')
                checks_24h = cursor.fetchone()[0]

                return {
                    'platform_stats': platform_stats,
                    'total_downloads': total_downloads,
                    'downloads_24h': downloads_24h,
                    'checks_24h': checks_24h,
                    'database_path': str(self.db_path),
                    'downloads_path': str(self.downloads_dir)
                }

        except Exception as e:
            self.logger.error(f"Failed to get stats: {e}")
            return {}


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Social Media Auto Tracker')
    parser.add_argument('command', choices=['daemon', 'add', 'remove', 'list', 'check', 'stats'],
                       help='Command to execute')
    parser.add_argument('--platform', choices=['instagram', 'tiktok', 'vsco'],
                       help='Platform for user operations')
    parser.add_argument('--username', help='Username to add/remove/check')
    parser.add_argument('--interval', type=int, default=1800,
                       help='Check interval in seconds (default: 1800 = 30 minutes)')
    parser.add_argument('--db-path', type=Path, help='Database file path')
    parser.add_argument('--downloads-dir', type=Path, help='Downloads directory')
    parser.add_argument('--no-posts', action='store_true', help='Disable post checking')
    parser.add_argument('--no-stories', action='store_true', help='Disable story checking')
    parser.add_argument('--no-reels', action='store_true', help='Disable reel checking')
    parser.add_argument('--session', help='Instagram session file path')

    args = parser.parse_args()

    # Initialize tracker
    tracker = AutoTracker(db_path=args.db_path, downloads_dir=args.downloads_dir)

    try:
        if args.command == 'daemon':
            # Start daemon
            tracker.start_daemon(args.interval)

        elif args.command == 'add':
            # Add user
            if not args.platform or not args.username:
                print("Error: --platform and --username required for add command")
                sys.exit(1)

            options = {
                'check_posts': not args.no_posts,
                'check_stories': not args.no_stories,
                'check_reels': not args.no_reels
            }

            # Handle Instagram session file
            if args.platform == 'instagram' and args.session:
                # Reinitialize Instagram downloader with session file
                try:
                    from instagram_downloader import InstagramDownloader
                    tracker.downloaders['instagram'] = InstagramDownloader(
                        session_file=args.session
                    )
                    print(f"Using Instagram session file: {args.session}")
                except Exception as e:
                    print(f"Failed to load Instagram session: {e}")
                    sys.exit(1)

            success = tracker.add_user(args.platform, args.username, **options)
            if success:
                print(f"Added {args.platform} @{args.username} to tracking")
            else:
                print(f"Failed to add {args.platform} @{args.username}")
                sys.exit(1)

        elif args.command == 'remove':
            # Remove user
            if not args.platform or not args.username:
                print("Error: --platform and --username required for remove command")
                sys.exit(1)

            success = tracker.remove_user(args.platform, args.username)
            if success:
                print(f"Removed {args.platform} @{args.username} from tracking")
            else:
                print(f"Failed to remove {args.platform} @{args.username}")
                sys.exit(1)

        elif args.command == 'list':
            # List users
            users = tracker.list_users(args.platform)

            if not users:
                print("No tracked users found")
            else:
                print(f"{'Platform':<12} {'Username':<20} {'Display Name':<25} {'Enabled':<8} {'Downloads':<10} {'Last Check'}")
                print("-" * 90)

                for user in users:
                    last_check = user['last_check']
                    if last_check:
                        last_check = datetime.fromisoformat(last_check).strftime('%Y-%m-%d %H:%M')
                    else:
                        last_check = 'Never'

                    print(f"{user['platform']:<12} @{user['username']:<19} {user['display_name']:<25} "
                          f"{'Yes' if user['enabled'] else 'No':<8} {user['total_downloads']:<10} {last_check}")

        elif args.command == 'check':
            # Manual check
            if args.platform and args.username:
                # Check specific user
                result = tracker.check_user_for_new_content(args.platform, args.username)
                if 'error' in result:
                    print(f"Error: {result['error']}")
                    sys.exit(1)
                else:
                    print(f"Check completed for {args.platform} @{args.username}:")
                    print(f"  Found: {result['found']} items")
                    print(f"  Downloaded: {result['downloaded']} items")
                    print(f"  Skipped: {result['skipped']} items")
                    print(f"  Errors: {result['errors']} items")
            else:
                # Check all users
                results = tracker.check_all_users()
                print(f"Check completed for {results['total_users']} users:")
                print(f"  Total found: {results['total_found']} items")
                print(f"  Total downloaded: {results['total_downloaded']} items")
                print(f"  Total errors: {results['total_errors']} items")

        elif args.command == 'stats':
            # Show statistics
            stats = tracker.get_stats()

            print("Auto Tracker Statistics")
            print("=" * 30)
            print(f"Database: {stats['database_path']}")
            print(f"Downloads: {stats['downloads_path']}")
            print(f"Total downloads: {stats['total_downloads']}")
            print(f"Downloads (24h): {stats['downloads_24h']}")
            print(f"Checks (24h): {stats['checks_24h']}")
            print()

            print("Platform Statistics:")
            for platform, data in stats['platform_stats'].items():
                print(f"  {platform.title()}: {data['enabled']}/{data['total']} users enabled")

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
