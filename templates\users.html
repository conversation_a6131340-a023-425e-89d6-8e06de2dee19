{% extends "base.html" %}

{% block title %}Users - Social Media Tracker{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">👥 User Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary btn-custom" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="fas fa-plus me-1"></i> Add User
        </button>
    </div>
</div>

<!-- Platform Tabs -->
<ul class="nav nav-tabs mb-4" id="platformTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="instagram-tab" data-bs-toggle="tab" data-bs-target="#instagram" type="button" role="tab">
            <i class="fab fa-instagram me-2 platform-instagram"></i>Instagram ({{ users_by_platform.get('instagram', [])|length }})
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="tiktok-tab" data-bs-toggle="tab" data-bs-target="#tiktok" type="button" role="tab">
            <i class="fab fa-tiktok me-2 platform-tiktok"></i>TikTok ({{ users_by_platform.get('tiktok', [])|length }})
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="vsco-tab" data-bs-toggle="tab" data-bs-target="#vsco" type="button" role="tab">
            <i class="fas fa-camera me-2 platform-vsco"></i>VSCO ({{ users_by_platform.get('vsco', [])|length }})
        </button>
    </li>
</ul>

<!-- Tab Content -->
<div class="tab-content" id="platformTabsContent">
    {% for platform, users in users_by_platform.items() %}
    <div class="tab-pane fade {% if platform == 'instagram' %}show active{% endif %}" id="{{ platform }}" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    {% if platform == 'instagram' %}
                        <i class="fab fa-instagram platform-instagram me-2"></i>Instagram Users
                    {% elif platform == 'tiktok' %}
                        <i class="fab fa-tiktok platform-tiktok me-2"></i>TikTok Users
                    {% else %}
                        <i class="fas fa-camera platform-vsco me-2"></i>VSCO Users
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                {% if users %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Username</th>
                                    <th>Status</th>
                                    <th>Added</th>
                                    <th>Last Check</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>
                                        <strong>@{{ user.username }}</strong>
                                    </td>
                                    <td>
                                        {% if user.enabled %}
                                            <span class="badge bg-success status-badge">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary status-badge">Disabled</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ user.created_at }}</small>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {% if user.last_check %}
                                                {{ user.last_check }}
                                            {% else %}
                                                Never
                                            {% endif %}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-primary" onclick="checkUser('{{ platform }}', '{{ user.username }}')">
                                                <i class="fas fa-sync"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" onclick="removeUser('{{ platform }}', '{{ user.username }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-user-plus fa-3x mb-3"></i>
                        <p>No {{ platform }} users added yet.</p>
                        <button type="button" class="btn btn-primary btn-custom" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            Add {{ platform.title() }} User
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="mb-3">
                        <label for="platform" class="form-label">Platform</label>
                        <select class="form-select" id="platform" name="platform" required>
                            <option value="">Select Platform</option>
                            <option value="instagram">Instagram</option>
                            <option value="tiktok">TikTok</option>
                            <option value="vsco">VSCO</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <div class="input-group">
                            <span class="input-group-text">@</span>
                            <input type="text" class="form-control" id="username" name="username" placeholder="username" required>
                        </div>
                        <div class="form-text">Enter username without @ symbol</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="addUser()">Add User</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function addUser() {
    const form = document.getElementById('addUserForm');
    const formData = new FormData(form);
    
    const data = {
        platform: formData.get('platform'),
        username: formData.get('username')
    };
    
    if (!data.platform || !data.username) {
        showAlert('Please fill in all fields', 'warning');
        return;
    }
    
    apiCall('/api/add_user', data, function() {
        // Close modal and refresh page
        const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
        modal.hide();
        location.reload();
    });
}

function removeUser(platform, username) {
    if (confirm(`Are you sure you want to remove ${platform} user @${username}?`)) {
        apiCall('/api/remove_user', {
            platform: platform,
            username: username
        }, function() {
            location.reload();
        });
    }
}

function checkUser(platform, username) {
    const btn = event.target;
    const originalHTML = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;
    
    apiCall('/api/check_user', {
        platform: platform,
        username: username
    }, function(data) {
        btn.innerHTML = originalHTML;
        btn.disabled = false;
        
        if (data.result) {
            showAlert(`Check completed for @${username}: Found ${data.result.found}, Downloaded ${data.result.downloaded}`, 'success');
        }
    });
}
</script>
{% endblock %}
