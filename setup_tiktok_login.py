#!/usr/bin/env python3
"""
TikTok Login Setup for Headless Server
Sets up TikTok username/password authentication
"""
import os
import getpass
from pathlib import Path

def setup_tiktok_login():
    """Setup TikTok login credentials"""
    print("🎵 TikTok Login Setup")
    print("=" * 40)
    print()
    print("Enter your TikTok credentials to access private users.")
    print("Credentials will be stored in config.py")
    print()
    
    # Get credentials
    username = input("TikTok username/email: ").strip()
    if not username:
        print("❌ Username required")
        return False
    
    password = getpass.getpass("TikTok password: ").strip()
    if not password:
        print("❌ Password required")
        return False
    
    # Update config file
    try:
        config_path = Path(__file__).parent / "config.py"
        
        if not config_path.exists():
            print(f"❌ Config file not found: {config_path}")
            return False
        
        # Read config
        with open(config_path, 'r') as f:
            content = f.read()
        
        # Update TikTok credentials
        content = content.replace(
            'TIKTOK_USERNAME = None',
            f'TIKTOK_USERNAME = "{username}"'
        )
        content = content.replace(
            'TIKTOK_PASSWORD = None',
            f'TIKTOK_PASSWORD = "{password}"'
        )
        
        # Write back
        with open(config_path, 'w') as f:
            f.write(content)
        
        print("✅ TikTok credentials saved to config.py")
        return True
        
    except Exception as e:
        print(f"❌ Failed to update config: {e}")
        return False

def test_tiktok_login():
    """Test TikTok login"""
    try:
        from tiktok_downloader import TikTokDownloader
        import config
        
        username = getattr(config, 'TIKTOK_USERNAME', None)
        password = getattr(config, 'TIKTOK_PASSWORD', None)
        
        if username and password:
            downloader = TikTokDownloader(username=username, password=password)
            print("✅ TikTok downloader initialized with login")
            print("🔓 Private TikTok users should now be accessible")
        else:
            print("❌ TikTok credentials not found in config")
            
    except Exception as e:
        print(f"❌ TikTok login test failed: {e}")

if __name__ == "__main__":
    print("🎵 TikTok Authentication Setup")
    print("=" * 50)
    
    if setup_tiktok_login():
        print()
        print("🧪 Testing TikTok login...")
        test_tiktok_login()
        
        print()
        print("🎯 NEXT STEPS:")
        print("1. Test with a private TikTok user:")
        print("   python3 auto_tracker.py add --platform tiktok --username private_user")
        print("   python3 auto_tracker.py check --platform tiktok --username private_user")
        print()
        print("2. Test with all TikTok users:")
        print("   python3 auto_tracker.py check --platform tiktok --username allusers")
        print()
        print("📝 NOTE: You must follow private users on TikTok to download their content.")
    else:
        print("❌ TikTok login setup failed")
