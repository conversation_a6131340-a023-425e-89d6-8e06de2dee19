#!/usr/bin/env python3
"""
Start the Social Media Tracker Web Dashboard
Installs dependencies and starts the web server
"""
import subprocess
import sys
import os
from pathlib import Path

def install_flask():
    """Install Flask if not available"""
    try:
        import flask
        print("✅ Flask already installed")
        return True
    except ImportError:
        print("📦 Installing Flask...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'flask'])
            print("✅ Flask installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install Flask: {e}")
            return False

def create_templates_dir():
    """Ensure templates directory exists"""
    templates_dir = Path(__file__).parent / "templates"
    if not templates_dir.exists():
        print("❌ Templates directory not found!")
        print("Please make sure all template files are in the 'templates' directory:")
        print("  - templates/base.html")
        print("  - templates/login.html") 
        print("  - templates/dashboard.html")
        print("  - templates/users.html")
        print("  - templates/logs.html")
        print("  - templates/settings.html")
        return False
    
    print("✅ Templates directory found")
    return True

def main():
    print("🌐 Social Media Tracker Dashboard Startup")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("auto_tracker.py").exists():
        print("❌ Please run this script from the social tracker directory")
        print("   cd ~/addition && python3 start_dashboard.py")
        sys.exit(1)
    
    # Install Flask
    if not install_flask():
        sys.exit(1)
    
    # Check templates
    if not create_templates_dir():
        sys.exit(1)
    
    # Start the dashboard
    print("\n🚀 Starting Web Dashboard...")
    print("📍 URL: http://*************:9990")
    print("👤 Username: ezek")
    print("🔑 Password: 5031Bryan!")
    print("\n⚠️  Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        # Import and run the dashboard
        from web_dashboard import app
        app.run(host="*************", port=9990, debug=False)
    except KeyboardInterrupt:
        print("\n\n👋 Dashboard stopped by user")
    except Exception as e:
        print(f"\n❌ Dashboard error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure all template files exist in templates/ directory")
        print("2. Check that port 9990 is not in use")
        print("3. Verify Flask is installed: pip install flask")

if __name__ == "__main__":
    main()
