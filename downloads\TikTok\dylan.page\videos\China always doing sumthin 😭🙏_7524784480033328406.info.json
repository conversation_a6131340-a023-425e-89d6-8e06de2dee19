{"id": "7524784480033328406", "formats": [{"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "download", "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oIfQLBC9LMe2fITaxkWbA5IQjAnzgPn8IPDQbs/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=2010&bt=1005&cs=0&ds=3&ft=4KJMyMzm8Zmo0DsmPI4jVxshQpWrKsd.&mime_type=video_mp4&qs=0&rc=OzQ3Nzw4ZzozOTc7Zjc3OEBpMzR4eW45cmZwNDMzbzczNUBeYjBeYjMuXjAxYDVfMzMuYSM2Z29zMmRzYmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=20250715235701559C009D9EB0DD21EF65&ply_type=2&policy=2&signature=39935205c392d424af48b441cc1c4f6a&tk=tt_chain_token", "format_note": "watermarked", "preference": -2, "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "dynamic_range": "SDR", "cookies": "ttwid=1%7C9h04yIYIlFugozcXl9rO4wir12-yXm8LTmvAdsOJl8Y%7C**********%7C65de6690ccdf5798128c28f858bf56ca19e0547ad776126506ae496ab0c8d57e; Domain=.tiktok.com; Path=/; Expires=1783727821; tt_csrf_token=wdXIHZLO-nIZUqBewgQmUNJnue-kyYnpUD5o; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"SURuNAVhzGe+1BsrmV7aIA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175822", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524784480033328406"}, "format": "download - unknown (watermarked)"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_1000692-0", "tbr": 1000, "quality": 1, "preference": -1, "filesize": 7859441, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/o4fQZ05fOAfngj65AQWk2ZIMn89CT8sQLIDttI/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1954&bt=977&cs=0&ds=6&ft=4KJMyMzm8Zmo0DsmPI4jVxshQpWrKsd.&mime_type=video_mp4&qs=0&rc=OmhnPGhkNzw4Ozg8aWlmZ0BpMzR4eW45cmZwNDMzbzczNUA0Mi0yMF8tNmIxNTEuLS0yYSM2Z29zMmRzYmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=20250715235701559C009D9EB0DD21EF65&ply_type=2&policy=2&signature=43401c511eac1293e9b44051cd741119&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C9h04yIYIlFugozcXl9rO4wir12-yXm8LTmvAdsOJl8Y%7C**********%7C65de6690ccdf5798128c28f858bf56ca19e0547ad776126506ae496ab0c8d57e; Domain=.tiktok.com; Path=/; Expires=1783727821; tt_csrf_token=wdXIHZLO-nIZUqBewgQmUNJnue-kyYnpUD5o; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"SURuNAVhzGe+1BsrmV7aIA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175822", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524784480033328406"}, "format": "h264_540p_1000692-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_1000692-1", "tbr": 1000, "quality": 1, "preference": -1, "filesize": 7859441, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/o4fQZ05fOAfngj65AQWk2ZIMn89CT8sQLIDttI/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1954&bt=977&cs=0&ds=6&ft=4KJMyMzm8Zmo0DsmPI4jVxshQpWrKsd.&mime_type=video_mp4&qs=0&rc=OmhnPGhkNzw4Ozg8aWlmZ0BpMzR4eW45cmZwNDMzbzczNUA0Mi0yMF8tNmIxNTEuLS0yYSM2Z29zMmRzYmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=20250715235701559C009D9EB0DD21EF65&ply_type=2&policy=2&signature=43401c511eac1293e9b44051cd741119&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C9h04yIYIlFugozcXl9rO4wir12-yXm8LTmvAdsOJl8Y%7C**********%7C65de6690ccdf5798128c28f858bf56ca19e0547ad776126506ae496ab0c8d57e; Domain=.tiktok.com; Path=/; Expires=1783727821; tt_csrf_token=wdXIHZLO-nIZUqBewgQmUNJnue-kyYnpUD5o; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"SURuNAVhzGe+1BsrmV7aIA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175822", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524784480033328406"}, "format": "h264_540p_1000692-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_423703-0", "tbr": 423, "quality": 1, "preference": -1, "filesize": 3327765, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/okItEQLLjaf2AtMQfWFv8hEeS5nInQDAIpsXDk/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=826&bt=413&cs=2&ds=6&ft=4KJMyMzm8Zmo0DsmPI4jVxshQpWrKsd.&mime_type=video_mp4&qs=11&rc=ZDdmZTc5aWY0ZDVlZDZmOkBpMzR4eW45cmZwNDMzbzczNUAyNjQ2Ll8yX18xX2MyY2I2YSM2Z29zMmRzYmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=20250715235701559C009D9EB0DD21EF65&ply_type=2&policy=2&signature=8690a6465dba1478360335e9c97c541b&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C9h04yIYIlFugozcXl9rO4wir12-yXm8LTmvAdsOJl8Y%7C**********%7C65de6690ccdf5798128c28f858bf56ca19e0547ad776126506ae496ab0c8d57e; Domain=.tiktok.com; Path=/; Expires=1783727821; tt_csrf_token=wdXIHZLO-nIZUqBewgQmUNJnue-kyYnpUD5o; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"SURuNAVhzGe+1BsrmV7aIA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175822", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524784480033328406"}, "format": "bytevc1_540p_423703-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_423703-1", "tbr": 423, "quality": 1, "preference": -1, "filesize": 3327765, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/okItEQLLjaf2AtMQfWFv8hEeS5nInQDAIpsXDk/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=826&bt=413&cs=2&ds=6&ft=4KJMyMzm8Zmo0DsmPI4jVxshQpWrKsd.&mime_type=video_mp4&qs=11&rc=ZDdmZTc5aWY0ZDVlZDZmOkBpMzR4eW45cmZwNDMzbzczNUAyNjQ2Ll8yX18xX2MyY2I2YSM2Z29zMmRzYmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=20250715235701559C009D9EB0DD21EF65&ply_type=2&policy=2&signature=8690a6465dba1478360335e9c97c541b&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C9h04yIYIlFugozcXl9rO4wir12-yXm8LTmvAdsOJl8Y%7C**********%7C65de6690ccdf5798128c28f858bf56ca19e0547ad776126506ae496ab0c8d57e; Domain=.tiktok.com; Path=/; Expires=1783727821; tt_csrf_token=wdXIHZLO-nIZUqBewgQmUNJnue-kyYnpUD5o; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"SURuNAVhzGe+1BsrmV7aIA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175822", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524784480033328406"}, "format": "bytevc1_540p_423703-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_525004-0", "tbr": 525, "quality": 2, "preference": -1, "filesize": 4123389, "width": 720, "height": 1280, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oATITTW2CQMcnngA5k3A89QfQaIujofLIHfs0D/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1024&bt=512&cs=2&ds=3&ft=4KJMyMzm8Zmo0DsmPI4jVxshQpWrKsd.&mime_type=video_mp4&qs=14&rc=ZmkzNzc6PGYzOTpmOGVpZkBpMzR4eW45cmZwNDMzbzczNUAuYC1iNTIzXjQxMS42Y181YSM2Z29zMmRzYmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=20250715235701559C009D9EB0DD21EF65&ply_type=2&policy=2&signature=e672bf85b73ec730d32e1c8868d4d3e5&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C9h04yIYIlFugozcXl9rO4wir12-yXm8LTmvAdsOJl8Y%7C**********%7C65de6690ccdf5798128c28f858bf56ca19e0547ad776126506ae496ab0c8d57e; Domain=.tiktok.com; Path=/; Expires=1783727821; tt_csrf_token=wdXIHZLO-nIZUqBewgQmUNJnue-kyYnpUD5o; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"SURuNAVhzGe+1BsrmV7aIA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175822", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524784480033328406"}, "format": "bytevc1_720p_525004-0 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_525004-1", "tbr": 525, "quality": 2, "preference": -1, "filesize": 4123389, "width": 720, "height": 1280, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oATITTW2CQMcnngA5k3A89QfQaIujofLIHfs0D/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1024&bt=512&cs=2&ds=3&ft=4KJMyMzm8Zmo0DsmPI4jVxshQpWrKsd.&mime_type=video_mp4&qs=14&rc=ZmkzNzc6PGYzOTpmOGVpZkBpMzR4eW45cmZwNDMzbzczNUAuYC1iNTIzXjQxMS42Y181YSM2Z29zMmRzYmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=20250715235701559C009D9EB0DD21EF65&ply_type=2&policy=2&signature=e672bf85b73ec730d32e1c8868d4d3e5&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C9h04yIYIlFugozcXl9rO4wir12-yXm8LTmvAdsOJl8Y%7C**********%7C65de6690ccdf5798128c28f858bf56ca19e0547ad776126506ae496ab0c8d57e; Domain=.tiktok.com; Path=/; Expires=1783727821; tt_csrf_token=wdXIHZLO-nIZUqBewgQmUNJnue-kyYnpUD5o; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"SURuNAVhzGe+1BsrmV7aIA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175822", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524784480033328406"}, "format": "bytevc1_720p_525004-1 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_808153-0", "tbr": 808, "quality": 3, "preference": -1, "filesize": 6347234, "width": 1080, "height": 1920, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oQhtnf5QDejfQEiIlDWQAaZMASIZGLOY8s2Ipn/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1578&bt=789&cs=2&ds=4&ft=4KJMyMzm8Zmo0DsmPI4jVxshQpWrKsd.&mime_type=video_mp4&qs=15&rc=aWU2aDY8OWgzZjQ0ODllZEBpMzR4eW45cmZwNDMzbzczNUBeXjBfLmEtXy0xLjQxYWE0YSM2Z29zMmRzYmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=20250715235701559C009D9EB0DD21EF65&ply_type=2&policy=2&signature=8ba16621e888321eeba94405a814d0b8&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C9h04yIYIlFugozcXl9rO4wir12-yXm8LTmvAdsOJl8Y%7C**********%7C65de6690ccdf5798128c28f858bf56ca19e0547ad776126506ae496ab0c8d57e; Domain=.tiktok.com; Path=/; Expires=1783727821; tt_csrf_token=wdXIHZLO-nIZUqBewgQmUNJnue-kyYnpUD5o; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"SURuNAVhzGe+1BsrmV7aIA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175822", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524784480033328406"}, "format": "bytevc1_1080p_808153-0 - 1080x1920"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_808153-1", "tbr": 808, "quality": 3, "preference": -1, "filesize": 6347234, "width": 1080, "height": 1920, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oQhtnf5QDejfQEiIlDWQAaZMASIZGLOY8s2Ipn/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1578&bt=789&cs=2&ds=4&ft=4KJMyMzm8Zmo0DsmPI4jVxshQpWrKsd.&mime_type=video_mp4&qs=15&rc=aWU2aDY8OWgzZjQ0ODllZEBpMzR4eW45cmZwNDMzbzczNUBeXjBfLmEtXy0xLjQxYWE0YSM2Z29zMmRzYmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=20250715235701559C009D9EB0DD21EF65&ply_type=2&policy=2&signature=8ba16621e888321eeba94405a814d0b8&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C9h04yIYIlFugozcXl9rO4wir12-yXm8LTmvAdsOJl8Y%7C**********%7C65de6690ccdf5798128c28f858bf56ca19e0547ad776126506ae496ab0c8d57e; Domain=.tiktok.com; Path=/; Expires=1783727821; tt_csrf_token=wdXIHZLO-nIZUqBewgQmUNJnue-kyYnpUD5o; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"SURuNAVhzGe+1BsrmV7aIA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175822", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524784480033328406"}, "format": "bytevc1_1080p_808153-1 - 1080x1920"}], "subtitles": {}, "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524784480033328406"}, "channel": "<PERSON>", "channel_id": "MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader": "dylan.page", "uploader_id": "6760368158794155013", "channel_url": "https://www.tiktok.com/@MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader_url": "https://www.tiktok.com/@dylan.page", "track": "original sound", "artists": ["<PERSON>"], "duration": 62, "title": "China always doing sumthin 😭🙏", "description": "China always doing sumthin 😭🙏", "timestamp": 1752000419, "view_count": 13900000, "like_count": 1600000, "repost_count": 55200, "comment_count": 8723, "thumbnails": [{"id": "dynamicCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oMCajskZQQIbIPLnzD2W1D5QQfMEfeATnA8X19~tplv-tiktokx-origin.image?dr=9636&x-expires=**********&x-signature=Fm1g9IPYvokudAeNfNV%2B36dODbY%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -2}, {"id": "cover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oUhtjsSFQIIhIcLnZD2WtV5QIfMDf8ApnA876e~tplv-tiktokx-origin.image?dr=9636&x-expires=**********&x-signature=dyvaKtqVMqXpoOcV1G94KQ6XgzQ%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}, {"id": "originCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/okdLf5QeBZjhQnfIWsARTIIMtAuI2qpDS68M6n~tplv-tiktokx-origin.image?dr=9636&x-expires=**********&x-signature=T8Dw1Z0XgunHSXHkWZjYajx0PVo%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}], "webpage_url": "https://www.tiktok.com/@dylan.page/video/7524784480033328406", "webpage_url_basename": "7524784480033328406", "webpage_url_domain": "tiktok.com", "extractor": "TikTok", "extractor_key": "TikTok", "thumbnail": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/okdLf5QeBZjhQnfIWsARTIIMtAuI2qpDS68M6n~tplv-tiktokx-origin.image?dr=9636&x-expires=**********&x-signature=T8Dw1Z0XgunHSXHkWZjYajx0PVo%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "display_id": "7524784480033328406", "fulltitle": "China always doing sumthin 😭🙏", "duration_string": "1:02", "upload_date": "20250708", "artist": "<PERSON>", "epoch": **********, "ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_423703-1", "tbr": 423, "quality": 1, "preference": -1, "filesize": 3327765, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/okItEQLLjaf2AtMQfWFv8hEeS5nInQDAIpsXDk/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=826&bt=413&cs=2&ds=6&ft=4KJMyMzm8Zmo0DsmPI4jVxshQpWrKsd.&mime_type=video_mp4&qs=11&rc=ZDdmZTc5aWY0ZDVlZDZmOkBpMzR4eW45cmZwNDMzbzczNUAyNjQ2Ll8yX18xX2MyY2I2YSM2Z29zMmRzYmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=**********&l=20250715235701559C009D9EB0DD21EF65&ply_type=2&policy=2&signature=8690a6465dba1478360335e9c97c541b&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7C9h04yIYIlFugozcXl9rO4wir12-yXm8LTmvAdsOJl8Y%7C**********%7C65de6690ccdf5798128c28f858bf56ca19e0547ad776126506ae496ab0c8d57e; Domain=.tiktok.com; Path=/; Expires=1783727821; tt_csrf_token=wdXIHZLO-nIZUqBewgQmUNJnue-kyYnpUD5o; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"SURuNAVhzGe+1BsrmV7aIA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175822", "format": "bytevc1_540p_423703-1 - 576x1024", "_type": "video", "_version": {"version": "2025.06.30", "release_git_head": "b0187844988e557c7e1e6bb1aabd4c1176768d86", "repository": "yt-dlp/yt-dlp"}}