"""
Simple File Manager for Social Media Downloads
"""
import os
import logging
from pathlib import Path
from typing import Dict, Optional
from datetime import datetime

import config


class FileManager:
    """Simple file manager for organizing downloads"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def organize_downloads(self, base_dir: Path, username: str, platform: str = None) -> Dict[str, Path]:
        """
        Create organized directory structure for downloads
        
        Args:
            base_dir: Base downloads directory
            username: Userna<PERSON> to create directories for
            platform: Platform name (instagram, tiktok, vsco)
            
        Returns:
            Dict mapping content types to their directories
        """
        base_dir = Path(base_dir)
        
        # Create platform-specific directory structure
        if platform == 'instagram':
            user_dir = base_dir / "Instagram" / username
            directories = {
                'posts': user_dir / "posts",
                'stories': user_dir / "stories", 
                'reels': user_dir / "reels",
                'highlights': user_dir / "highlights",
                'profile': user_dir / "profile"
            }
        elif platform == 'tiktok':
            user_dir = base_dir / "TikTok" / username
            directories = {
                'videos': user_dir / "videos",
                'profile': user_dir / "profile"
            }
        elif platform == 'vsco':
            user_dir = base_dir / "VSCO" / username
            directories = {
                'images': user_dir / "images",
                'profile': user_dir / "profile"
            }
        else:
            # Generic structure
            user_dir = base_dir / username
            directories = {
                'posts': user_dir / "posts",
                'media': user_dir / "media",
                'profile': user_dir / "profile"
            }
        
        # Create all directories
        for content_type, directory in directories.items():
            directory.mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"Created directory: {directory}")
        
        return directories
    
    def get_safe_filename(self, filename: str, max_length: int = 200) -> str:
        """
        Create a safe filename by removing invalid characters
        
        Args:
            filename: Original filename
            max_length: Maximum filename length
            
        Returns:
            Safe filename string
        """
        # Remove invalid characters
        invalid_chars = '<>:"/\\|?*'
        safe_filename = filename
        
        for char in invalid_chars:
            safe_filename = safe_filename.replace(char, '_')
        
        # Remove multiple underscores
        while '__' in safe_filename:
            safe_filename = safe_filename.replace('__', '_')
        
        # Trim length
        if len(safe_filename) > max_length:
            name, ext = os.path.splitext(safe_filename)
            safe_filename = name[:max_length-len(ext)] + ext
        
        return safe_filename.strip('_')
    
    def create_dated_filename(self, base_name: str, extension: str, date: datetime = None) -> str:
        """
        Create filename with date prefix
        
        Args:
            base_name: Base filename
            extension: File extension (with or without dot)
            date: Date to use (defaults to now)
            
        Returns:
            Filename with date prefix
        """
        if date is None:
            date = datetime.now()
        
        # Ensure extension has dot
        if not extension.startswith('.'):
            extension = '.' + extension
        
        # Format date
        date_str = date.strftime('%Y%m%d_%H%M%S')
        
        # Create safe base name
        safe_base = self.get_safe_filename(base_name)
        
        return f"{date_str}_{safe_base}{extension}"
    
    def ensure_directory_exists(self, directory: Path) -> bool:
        """
        Ensure directory exists, create if necessary
        
        Args:
            directory: Directory path to check/create
            
        Returns:
            True if directory exists or was created successfully
        """
        try:
            directory.mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            self.logger.error(f"Failed to create directory {directory}: {e}")
            return False
    
    def get_file_size(self, file_path: Path) -> Optional[int]:
        """
        Get file size in bytes
        
        Args:
            file_path: Path to file
            
        Returns:
            File size in bytes or None if error
        """
        try:
            return file_path.stat().st_size
        except Exception as e:
            self.logger.error(f"Failed to get file size for {file_path}: {e}")
            return None
    
    def cleanup_empty_directories(self, base_dir: Path):
        """
        Remove empty directories recursively
        
        Args:
            base_dir: Base directory to clean up
        """
        try:
            for root, dirs, files in os.walk(base_dir, topdown=False):
                for directory in dirs:
                    dir_path = Path(root) / directory
                    try:
                        if not any(dir_path.iterdir()):
                            dir_path.rmdir()
                            self.logger.debug(f"Removed empty directory: {dir_path}")
                    except OSError:
                        pass  # Directory not empty or other error
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def get_download_path(self, base_dir: Path, username: str, content_type: str, 
                         filename: str, platform: str = None) -> Path:
        """
        Get full download path for a file
        
        Args:
            base_dir: Base downloads directory
            username: Username
            content_type: Type of content (posts, stories, etc.)
            filename: Filename
            platform: Platform name
            
        Returns:
            Full path for the download
        """
        directories = self.organize_downloads(base_dir, username, platform)
        
        if content_type in directories:
            return directories[content_type] / filename
        else:
            # Fallback to generic directory
            return directories.get('media', directories['profile']) / filename
