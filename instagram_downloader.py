"""
Instagram Downloader - Download posts, stories, reels, and highlights
"""
import os
import logging
import time
import random
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any
import instaloader
from instaloader import Profile, Post, Story, Highlight
import config

# Simple error classes
class AuthenticationError(Exception):
    pass

class NetworkError(Exception):
    pass

class ContentError(Exception):
    pass

class RateLimitError(Exception):
    pass

# Simple retry decorator
def retry_on_error(max_retries=3, delay=1):
    def decorator(func):
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise
                    time.sleep(delay * (attempt + 1))
            return None
        return wrapper
    return decorator
from file_manager import FileManager


class InstagramDownloader:
    """Main class for downloading Instagram content"""

    def __init__(self, username: Optional[str] = None, password: Optional[str] = None,
                 session_file: Optional[str] = None):
        """
        Initialize the Instagram downloader

        Args:
            username: Instagram username for login (optional for public content)
            password: Instagram password for login (optional for public content)
            session_file: Path to session file for authentication (recommended)
        """
        # Initialize instaloader with very conservative settings for continuous operation
        self.loader = instaloader.Instaloader(
            download_videos=True,
            download_video_thumbnails=False,
            download_geotags=False,
            download_comments=False,
            save_metadata=True,
            compress_json=False,
            dirname_pattern=str(config.DOWNLOADS_DIR / "Instagram" / "{profile}"),
            # Very conservative rate limiting for continuous operation
            sleep=lambda: random.uniform(10, 30)  # 10-30 seconds between requests
        )
        
        self.username = username
        self.password = password
        self.session_file = session_file
        self.logged_in = False

        # Initialize components
        self.file_manager = FileManager()

        # Setup logging
        self._setup_logging()

        # Try to load session file first, then login with credentials
        if session_file and os.path.exists(session_file):
            self.load_session()
        elif username and password:
            self.login()
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_file = config.LOGS_DIR / f"instagram_downloader_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=getattr(logging, config.LOG_LEVEL),
            format=config.LOG_FORMAT,
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)

    def _conservative_delay(self, min_seconds: int = 5, max_seconds: int = 15):
        """
        Add conservative delay between Instagram operations to avoid rate limiting

        Args:
            min_seconds: Minimum delay in seconds
            max_seconds: Maximum delay in seconds
        """
        delay = random.uniform(min_seconds, max_seconds)
        self.logger.debug(f"Waiting {delay:.1f} seconds to avoid rate limiting...")
        time.sleep(delay)

    def _operation_delay(self):
        """Standard delay between major operations (posts, stories, reels)"""
        self._conservative_delay(15, 30)  # 15-30 seconds between operations

    def _request_delay(self):
        """Standard delay between individual requests"""
        self._conservative_delay(5, 10)   # 5-10 seconds between requests

    def _ensure_user_directories(self, username: str) -> Dict[str, Path]:
        """
        Ensure user-specific directories exist and return their paths

        Args:
            username: Instagram username

        Returns:
            Dict[str, Path]: Dictionary with paths for each content type
        """
        # Create user-specific directories using file manager
        user_directories = self.file_manager.organize_downloads(config.DOWNLOADS_DIR, username, 'instagram')

        self.logger.info(f"Created/verified directories for user: {username}")
        for content_type, path in user_directories.items():
            self.logger.debug(f"  {content_type}: {path}")

        return user_directories

    def load_session(self) -> bool:
        """
        Load Instagram session from file

        Returns:
            True if session loaded successfully, False otherwise
        """
        try:
            if not self.session_file or not os.path.exists(self.session_file):
                self.logger.warning("Session file not found")
                return False

            self.loader.load_session_from_file(self.username or "session", self.session_file)
            self.logged_in = True
            self.logger.info(f"Session loaded successfully from {self.session_file}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to load session: {str(e)}")
            return False

    def save_session(self, filename: Optional[str] = None) -> bool:
        """
        Save current Instagram session to file

        Args:
            filename: Optional filename to save session to

        Returns:
            True if session saved successfully, False otherwise
        """
        try:
            if not self.logged_in:
                self.logger.warning("Not logged in, cannot save session")
                return False

            session_file = filename or self.session_file or f"{self.username}_session"
            self.loader.save_session_to_file(session_file)
            self.logger.info(f"Session saved to {session_file}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to save session: {str(e)}")
            return False

    def login(self) -> bool:
        """
        Login to Instagram
        
        Returns:
            bool: True if login successful, False otherwise
        """
        try:
            if self.username and self.password:
                self.loader.login(self.username, self.password)
                self.logged_in = True
                self.logger.info(f"Successfully logged in as {self.username}")
                return True
        except Exception as e:
            self.logger.error(f"Login failed: {str(e)}")
            return False
        
        return False
    
    def _generate_filename(self, username: str, date: datetime, media_type: str, 
                          index: int = 0, extension: str = "") -> str:
        """
        Generate filename based on configuration
        
        Args:
            username: Instagram username
            date: Date when content was posted
            media_type: Type of media (post, story, reel, highlight)
            index: Index for multiple files
            extension: File extension
            
        Returns:
            str: Generated filename
        """
        date_str = date.strftime(config.DATE_FORMAT)
        filename = config.FILENAME_FORMAT.format(
            username=username,
            date=date_str,
            media_type=media_type,
            index=index
        )
        return f"{filename}{extension}"
    
    @retry_on_error()
    def download_posts(self, username: str, count: Optional[int] = None) -> bool:
        """
        Download posts from a user's profile

        Args:
            username: Instagram username
            count: Maximum number of posts to download (None for all)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure user directories exist
            user_dirs = self._ensure_user_directories(username)

            # Add delay before starting posts download
            self._operation_delay()

            profile = Profile.from_username(self.loader.context, username)
            self.logger.info(f"Downloading posts from @{username} to {user_dirs['posts']}")

            # Add delay after profile lookup
            self._request_delay()

            posts = profile.get_posts()
            if count:
                posts = list(posts)[:count]

            # Temporarily change the loader's dirname pattern to save to user's posts folder
            original_pattern = self.loader.dirname_pattern
            self.loader.dirname_pattern = str(user_dirs['posts'])

            try:
                for i, post in enumerate(posts):
                    try:
                        # Add delay between each post download
                        if i > 0:  # Don't delay before first post
                            self._request_delay()

                        self.loader.download_post(post, target="")
                        self.logger.info(f"Downloaded post: {post.shortcode}")
                    except Exception as e:
                        self.logger.error(f"Failed to download post {post.shortcode}: {str(e)}")
                        # Add extra delay after errors
                        self._conservative_delay(10, 20)
            finally:
                # Restore original pattern
                self.loader.dirname_pattern = original_pattern

            return True

        except Exception as e:
            self.logger.error(f"Failed to download posts from @{username}: {str(e)}")
            return False
    
    @retry_on_error()
    def download_stories(self, username: str) -> bool:
        """
        Download stories from a user's profile

        Args:
            username: Instagram username

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure user directories exist
            user_dirs = self._ensure_user_directories(username)

            # Add delay before starting stories download
            self._operation_delay()

            profile = Profile.from_username(self.loader.context, username)
            self.logger.info(f"Downloading stories from @{username} to {user_dirs['stories']}")

            # Add delay after profile lookup
            self._request_delay()

            # Temporarily change the loader's dirname pattern to save to user's stories folder
            original_pattern = self.loader.dirname_pattern
            self.loader.dirname_pattern = str(user_dirs['stories'])

            try:
                story_count = 0
                for story in self.loader.get_stories([profile.userid]):
                    # Add delay between stories
                    if story_count > 0:
                        self._request_delay()

                    item_count = 0
                    for item in story.get_items():
                        try:
                            # Add delay between story items
                            if item_count > 0:
                                self._conservative_delay(3, 8)

                            self.loader.download_storyitem(item, target="")
                            self.logger.info(f"Downloaded story item: {item.mediaid}")
                            item_count += 1
                        except Exception as e:
                            self.logger.error(f"Failed to download story item: {str(e)}")
            finally:
                # Restore original pattern
                self.loader.dirname_pattern = original_pattern

            return True

        except Exception as e:
            self.logger.error(f"Failed to download stories from @{username}: {str(e)}")
            return False

    @retry_on_error()
    def download_reels(self, username: str, count: Optional[int] = None) -> bool:
        """
        Download reels from a user's profile

        Args:
            username: Instagram username
            count: Maximum number of reels to download (None for all)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure user directories exist
            user_dirs = self._ensure_user_directories(username)

            # Add delay before starting reels download
            self._operation_delay()

            profile = Profile.from_username(self.loader.context, username)
            self.logger.info(f"Downloading reels from @{username} to {user_dirs['reels']}")

            # Add delay after profile lookup
            self._request_delay()

            # Get posts and filter for reels (video posts)
            posts = profile.get_posts()
            reels = [post for post in posts if post.is_video]

            if count:
                reels = reels[:count]

            # Temporarily change the loader's dirname pattern to save to user's reels folder
            original_pattern = self.loader.dirname_pattern
            self.loader.dirname_pattern = str(user_dirs['reels'])

            try:
                for i, reel in enumerate(reels):
                    try:
                        # Add delay between each reel download
                        if i > 0:  # Don't delay before first reel
                            self._request_delay()

                        self.loader.download_post(reel, target="")
                        self.logger.info(f"Downloaded reel: {reel.shortcode}")
                    except Exception as e:
                        self.logger.error(f"Failed to download reel {reel.shortcode}: {str(e)}")
                        # Add extra delay after errors
                        self._conservative_delay(10, 20)
            finally:
                # Restore original pattern
                self.loader.dirname_pattern = original_pattern

            return True

        except Exception as e:
            self.logger.error(f"Failed to download reels from @{username}: {str(e)}")
            return False

    @retry_on_error()
    def download_highlights(self, username: str) -> bool:
        """
        Download highlights from a user's profile

        Args:
            username: Instagram username

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure user directories exist
            user_dirs = self._ensure_user_directories(username)

            # Add delay before starting highlights download
            self._operation_delay()

            profile = Profile.from_username(self.loader.context, username)
            self.logger.info(f"Downloading highlights from @{username} to {user_dirs['highlights']}")

            # Add delay after profile lookup
            self._request_delay()

            # Temporarily change the loader's dirname pattern to save to user's highlights folder
            original_pattern = self.loader.dirname_pattern
            self.loader.dirname_pattern = str(user_dirs['highlights'])

            try:
                highlight_count = 0
                for highlight in self.loader.get_highlights(profile):
                    try:
                        # Add delay between each highlight download
                        if highlight_count > 0:
                            self._request_delay()

                        self.loader.download_highlight(highlight, target="")
                        self.logger.info(f"Downloaded highlight: {highlight.title}")
                        highlight_count += 1
                    except Exception as e:
                        self.logger.error(f"Failed to download highlight {highlight.title}: {str(e)}")
                        # Add extra delay after errors
                        self._conservative_delay(10, 20)
            finally:
                # Restore original pattern
                self.loader.dirname_pattern = original_pattern

            return True

        except Exception as e:
            self.logger.error(f"Failed to download highlights from @{username}: {str(e)}")
            return False

    def download_all(self, username: str, include_posts: bool = True,
                    include_stories: bool = True, include_reels: bool = True,
                    include_highlights: bool = True, posts_count: Optional[int] = None,
                    reels_count: Optional[int] = None) -> Dict[str, bool]:
        """
        Download all types of content from a user's profile

        Args:
            username: Instagram username
            include_posts: Whether to download posts
            include_stories: Whether to download stories
            include_reels: Whether to download reels
            include_highlights: Whether to download highlights
            posts_count: Maximum number of posts to download
            reels_count: Maximum number of reels to download

        Returns:
            Dict[str, bool]: Results for each content type
        """
        results = {}

        if include_posts:
            results['posts'] = self.download_posts(username, posts_count)

        if include_stories:
            results['stories'] = self.download_stories(username)

        if include_reels:
            results['reels'] = self.download_reels(username, reels_count)

        if include_highlights:
            results['highlights'] = self.download_highlights(username)

        return results

    def get_user_download_path(self, username: str) -> Path:
        """
        Get the download path for a specific user

        Args:
            username: Instagram username

        Returns:
            Path: Path to user's download directory
        """
        return config.DOWNLOADS_DIR / username

    def show_download_summary(self, username: str, results: Dict[str, bool]):
        """
        Show a summary of what was downloaded and where

        Args:
            username: Instagram username
            results: Results from download_all method
        """
        user_path = self.get_user_download_path(username)
        self.logger.info(f"\n📁 Downloads for @{username} saved to: {user_path}")

        user_dirs = self._ensure_user_directories(username)
        for content_type, success in results.items():
            if success:
                content_path = user_dirs[content_type]
                # Count files in directory
                try:
                    file_count = len([f for f in content_path.iterdir() if f.is_file()])
                    self.logger.info(f"  ✅ {content_type.capitalize()}: {file_count} files in {content_path}")
                except Exception:
                    self.logger.info(f"  ✅ {content_type.capitalize()}: saved to {content_path}")
            else:
                self.logger.info(f"  ❌ {content_type.capitalize()}: failed to download")
