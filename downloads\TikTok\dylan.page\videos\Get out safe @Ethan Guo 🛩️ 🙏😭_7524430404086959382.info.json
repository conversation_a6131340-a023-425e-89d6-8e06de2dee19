{"id": "7524430404086959382", "formats": [{"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "download", "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oIDs3kdoEB9beIEiMfRPEouF4Uo7CglhQwARFo/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1918&bt=959&cs=0&ds=3&ft=4KJMyMzm8Zmo03smPI4jVOehQpWrKsd.&mime_type=video_mp4&qs=0&rc=NzM5ODc0aGY0aDY5ZztlZ0Bpajh1N3E5cmQ1NDMzbzczNUBiNmIvLTMuNmMxNDJgLV9eYSNxbzRgMmQ0XmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796775&l=20250715235703F5885A931189AB297292&ply_type=2&policy=2&signature=251836945e77a0d8ff34f05647dbce33&tk=tt_chain_token", "format_note": "watermarked", "preference": -2, "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "dynamic_range": "SDR", "cookies": "ttwid=1%7Cb4VDDhMuiu210vBNpuwSX6y6mE3-Kd8V_KohiegkH6I%7C1752623822%7C5eca08c476400d14e217dbbd6e50aae83890316e48cece637820230753bb18fa; Domain=.tiktok.com; Path=/; Expires=1783727822; tt_csrf_token=Nv7oam43-bndgxVxY3eXfmxvAs6t4U4YPaEg; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"GxvQVUpMnn24jeD3x5Z0SA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175823", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524430404086959382"}, "format": "download - unknown (watermarked)"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_956713-0", "tbr": 956, "quality": 1, "preference": -1, "filesize": 18245362, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oALFUfDpFiLobSY5zR42AB7Ewu9QIkgfgoChjE/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1868&bt=934&cs=0&ds=6&ft=4KJMyMzm8Zmo03smPI4jVOehQpWrKsd.&mime_type=video_mp4&qs=0&rc=OGQ4ZmU0OWQ1OTc4aWc5N0Bpajh1N3E5cmQ1NDMzbzczNUAwYzJjYzBhX2MxMzEtYGNgYSNxbzRgMmQ0XmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796775&l=20250715235703F5885A931189AB297292&ply_type=2&policy=2&signature=503ccbbbec716eab5265c3872fd4a9d6&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cb4VDDhMuiu210vBNpuwSX6y6mE3-Kd8V_KohiegkH6I%7C1752623822%7C5eca08c476400d14e217dbbd6e50aae83890316e48cece637820230753bb18fa; Domain=.tiktok.com; Path=/; Expires=1783727822; tt_csrf_token=Nv7oam43-bndgxVxY3eXfmxvAs6t4U4YPaEg; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"GxvQVUpMnn24jeD3x5Z0SA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175823", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524430404086959382"}, "format": "h264_540p_956713-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h264", "acodec": "aac", "format_id": "h264_540p_956713-1", "tbr": 956, "quality": 1, "preference": -1, "filesize": 18245362, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068c001-no/oALFUfDpFiLobSY5zR42AB7Ewu9QIkgfgoChjE/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1868&bt=934&cs=0&ds=6&ft=4KJMyMzm8Zmo03smPI4jVOehQpWrKsd.&mime_type=video_mp4&qs=0&rc=OGQ4ZmU0OWQ1OTc4aWc5N0Bpajh1N3E5cmQ1NDMzbzczNUAwYzJjYzBhX2MxMzEtYGNgYSNxbzRgMmQ0XmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796775&l=20250715235703F5885A931189AB297292&ply_type=2&policy=2&signature=503ccbbbec716eab5265c3872fd4a9d6&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cb4VDDhMuiu210vBNpuwSX6y6mE3-Kd8V_KohiegkH6I%7C1752623822%7C5eca08c476400d14e217dbbd6e50aae83890316e48cece637820230753bb18fa; Domain=.tiktok.com; Path=/; Expires=1783727822; tt_csrf_token=Nv7oam43-bndgxVxY3eXfmxvAs6t4U4YPaEg; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"GxvQVUpMnn24jeD3x5Z0SA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175823", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524430404086959382"}, "format": "h264_540p_956713-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_406241-0", "tbr": 406, "quality": 1, "preference": -1, "filesize": 7747379, "width": 576, "height": 1024, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/o4CwkFDGREFgVEYoFsEIhUBAKQ9gouHebof7l1/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=792&bt=396&cs=2&ds=6&ft=4KJMyMzm8Zmo03smPI4jVOehQpWrKsd.&mime_type=video_mp4&qs=11&rc=N2ZlZWc4ODs2ZGllNDw7ZUBpajh1N3E5cmQ1NDMzbzczNUAvXmJjLmMuNWIxMzI1LTQwYSNxbzRgMmQ0XmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796775&l=20250715235703F5885A931189AB297292&ply_type=2&policy=2&signature=73ebf467b1adf10b71c65398399eceee&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cb4VDDhMuiu210vBNpuwSX6y6mE3-Kd8V_KohiegkH6I%7C1752623822%7C5eca08c476400d14e217dbbd6e50aae83890316e48cece637820230753bb18fa; Domain=.tiktok.com; Path=/; Expires=1783727822; tt_csrf_token=Nv7oam43-bndgxVxY3eXfmxvAs6t4U4YPaEg; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"GxvQVUpMnn24jeD3x5Z0SA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175823", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524430404086959382"}, "format": "bytevc1_540p_406241-0 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_406241-1", "tbr": 406, "quality": 1, "preference": -1, "filesize": 7747379, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/o4CwkFDGREFgVEYoFsEIhUBAKQ9gouHebof7l1/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=792&bt=396&cs=2&ds=6&ft=4KJMyMzm8Zmo03smPI4jVOehQpWrKsd.&mime_type=video_mp4&qs=11&rc=N2ZlZWc4ODs2ZGllNDw7ZUBpajh1N3E5cmQ1NDMzbzczNUAvXmJjLmMuNWIxMzI1LTQwYSNxbzRgMmQ0XmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796775&l=20250715235703F5885A931189AB297292&ply_type=2&policy=2&signature=73ebf467b1adf10b71c65398399eceee&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cb4VDDhMuiu210vBNpuwSX6y6mE3-Kd8V_KohiegkH6I%7C1752623822%7C5eca08c476400d14e217dbbd6e50aae83890316e48cece637820230753bb18fa; Domain=.tiktok.com; Path=/; Expires=1783727822; tt_csrf_token=Nv7oam43-bndgxVxY3eXfmxvAs6t4U4YPaEg; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"GxvQVUpMnn24jeD3x5Z0SA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175823", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524430404086959382"}, "format": "bytevc1_540p_406241-1 - 576x1024"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_498302-0", "tbr": 498, "quality": 2, "preference": -1, "filesize": 9503060, "width": 720, "height": 1280, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oggJ9ECeoFEXKlouADB1BzSbFQUIkEfR3Ehws7/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=972&bt=486&cs=2&ds=3&ft=4KJMyMzm8Zmo03smPI4jVOehQpWrKsd.&mime_type=video_mp4&qs=14&rc=ODw0NjdpZGlmaTU3Omg8Z0Bpajh1N3E5cmQ1NDMzbzczNUAwNi4xYl9iNTYxMy0uYi0tYSNxbzRgMmQ0XmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796775&l=20250715235703F5885A931189AB297292&ply_type=2&policy=2&signature=f55de3f4d99479e495718805360c2d80&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cb4VDDhMuiu210vBNpuwSX6y6mE3-Kd8V_KohiegkH6I%7C1752623822%7C5eca08c476400d14e217dbbd6e50aae83890316e48cece637820230753bb18fa; Domain=.tiktok.com; Path=/; Expires=1783727822; tt_csrf_token=Nv7oam43-bndgxVxY3eXfmxvAs6t4U4YPaEg; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"GxvQVUpMnn24jeD3x5Z0SA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175823", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524430404086959382"}, "format": "bytevc1_720p_498302-0 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_720p_498302-1", "tbr": 498, "quality": 2, "preference": -1, "filesize": 9503060, "width": 720, "height": 1280, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/oggJ9ECeoFEXKlouADB1BzSbFQUIkEfR3Ehws7/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=972&bt=486&cs=2&ds=3&ft=4KJMyMzm8Zmo03smPI4jVOehQpWrKsd.&mime_type=video_mp4&qs=14&rc=ODw0NjdpZGlmaTU3Omg8Z0Bpajh1N3E5cmQ1NDMzbzczNUAwNi4xYl9iNTYxMy0uYi0tYSNxbzRgMmQ0XmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796775&l=20250715235703F5885A931189AB297292&ply_type=2&policy=2&signature=f55de3f4d99479e495718805360c2d80&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "720x1280", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cb4VDDhMuiu210vBNpuwSX6y6mE3-Kd8V_KohiegkH6I%7C1752623822%7C5eca08c476400d14e217dbbd6e50aae83890316e48cece637820230753bb18fa; Domain=.tiktok.com; Path=/; Expires=1783727822; tt_csrf_token=Nv7oam43-bndgxVxY3eXfmxvAs6t4U4YPaEg; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"GxvQVUpMnn24jeD3x5Z0SA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175823", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524430404086959382"}, "format": "bytevc1_720p_498302-1 - 720x1280"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_899597-0", "tbr": 899, "quality": 3, "preference": -1, "filesize": 17152626, "width": 1080, "height": 1920, "url": "https://v16-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/ocAgYF7oUbw0DVIgRfhBCrEugkEKFztILoe9Q2/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1756&bt=878&cs=2&ds=4&ft=4KJMyMzm8Zmo03smPI4jVOehQpWrKsd.&mime_type=video_mp4&qs=15&rc=ZzQzNDU6aDdmOjNnOjppZUBpajh1N3E5cmQ1NDMzbzczNUA1NmAxNV8vNWMxYzRiXmBjYSNxbzRgMmQ0XmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796775&l=20250715235703F5885A931189AB297292&ply_type=2&policy=2&signature=c6555539c43e3985ea1e186b54d28387&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cb4VDDhMuiu210vBNpuwSX6y6mE3-Kd8V_KohiegkH6I%7C1752623822%7C5eca08c476400d14e217dbbd6e50aae83890316e48cece637820230753bb18fa; Domain=.tiktok.com; Path=/; Expires=1783727822; tt_csrf_token=Nv7oam43-bndgxVxY3eXfmxvAs6t4U4YPaEg; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"GxvQVUpMnn24jeD3x5Z0SA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175823", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524430404086959382"}, "format": "bytevc1_1080p_899597-0 - 1080x1920"}, {"ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_1080p_899597-1", "tbr": 899, "quality": 3, "preference": -1, "filesize": 17152626, "width": 1080, "height": 1920, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/ocAgYF7oUbw0DVIgRfhBCrEugkEKFztILoe9Q2/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=1756&bt=878&cs=2&ds=4&ft=4KJMyMzm8Zmo03smPI4jVOehQpWrKsd.&mime_type=video_mp4&qs=15&rc=ZzQzNDU6aDdmOjNnOjppZUBpajh1N3E5cmQ1NDMzbzczNUA1NmAxNV8vNWMxYzRiXmBjYSNxbzRgMmQ0XmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796775&l=20250715235703F5885A931189AB297292&ply_type=2&policy=2&signature=c6555539c43e3985ea1e186b54d28387&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "1080x1920", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cb4VDDhMuiu210vBNpuwSX6y6mE3-Kd8V_KohiegkH6I%7C1752623822%7C5eca08c476400d14e217dbbd6e50aae83890316e48cece637820230753bb18fa; Domain=.tiktok.com; Path=/; Expires=1783727822; tt_csrf_token=Nv7oam43-bndgxVxY3eXfmxvAs6t4U4YPaEg; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"GxvQVUpMnn24jeD3x5Z0SA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175823", "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524430404086959382"}, "format": "bytevc1_1080p_899597-1 - 1080x1920"}], "subtitles": {}, "http_headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "Accept-Language": "en-us,en;q=0.5", "Sec-Fetch-Mode": "navigate", "Referer": "https://www.tiktok.com/@dylan.page/video/7524430404086959382"}, "channel": "<PERSON>", "channel_id": "MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader": "dylan.page", "uploader_id": "6760368158794155013", "channel_url": "https://www.tiktok.com/@MS4wLjABAAAAm3_z9-GdAYI_Ze5E1GT_lp6napMh63gnn8TPJiQ5Mbpbw183U2F7tYXRkNPFFUN8", "uploader_url": "https://www.tiktok.com/@dylan.page", "track": "original sound", "artists": ["<PERSON>"], "duration": 152, "title": "Get out safe @<PERSON> Guo 🛩️ 🙏😭", "description": "Get out safe @<PERSON> Guo 🛩️ 🙏😭", "timestamp": 1751917978, "view_count": 21200000, "like_count": 2500000, "repost_count": 34700, "comment_count": 11800, "thumbnails": [{"id": "dynamicCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/oQ3E2fCY7goRDeRQE9uEAoBkSFbFQNphoLUwsD~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=43UNVSzphMQwzWAvlPN3YllcbA8%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -2}, {"id": "cover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/osesolgbfAIyRUlXDDRohbB7JEFkFEy9wC3I0u~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=8UAwiIw7roKs9z6WwOqhJrvP0do%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}, {"id": "originCover", "url": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/ogtEeJ9bIwuEkSbBlswDfF7RUoAFB5CgZXhoIh~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=Rkort8qXwHCZ1s4o78kiBLFWXmU%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "preference": -1}], "webpage_url": "https://www.tiktok.com/@dylan.page/video/7524430404086959382", "webpage_url_basename": "7524430404086959382", "webpage_url_domain": "tiktok.com", "extractor": "TikTok", "extractor_key": "TikTok", "thumbnail": "https://p16-common-sign-no.tiktokcdn-us.com/tos-no1a-p-0037-no/ogtEeJ9bIwuEkSbBlswDfF7RUoAFB5CgZXhoIh~tplv-tiktokx-origin.image?dr=9636&x-expires=1752793200&x-signature=Rkort8qXwHCZ1s4o78kiBLFWXmU%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast8", "display_id": "7524430404086959382", "fulltitle": "Get out safe @<PERSON> Guo 🛩️ 🙏😭", "duration_string": "2:32", "upload_date": "20250707", "artist": "<PERSON>", "epoch": 1752623823, "ext": "mp4", "vcodec": "h265", "acodec": "aac", "format_id": "bytevc1_540p_406241-1", "tbr": 406, "quality": 1, "preference": -1, "filesize": 7747379, "width": 576, "height": 1024, "url": "https://v19-webapp-prime.us.tiktok.com/video/tos/no1a/tos-no1a-ve-0068-no/o4CwkFDGREFgVEYoFsEIhUBAKQ9gouHebof7l1/?a=1988&bti=ODszNWYuMDE6&ch=0&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C&cv=1&br=792&bt=396&cs=2&ds=6&ft=4KJMyMzm8Zmo03smPI4jVOehQpWrKsd.&mime_type=video_mp4&qs=11&rc=N2ZlZWc4ODs2ZGllNDw7ZUBpajh1N3E5cmQ1NDMzbzczNUAvXmJjLmMuNWIxMzI1LTQwYSNxbzRgMmQ0XmphLS1kMTFzcw%3D%3D&btag=e00090000&expire=1752796775&l=20250715235703F5885A931189AB297292&ply_type=2&policy=2&signature=73ebf467b1adf10b71c65398399eceee&tk=tt_chain_token", "protocol": "https", "video_ext": "mp4", "audio_ext": "none", "resolution": "576x1024", "dynamic_range": "SDR", "aspect_ratio": 0.56, "cookies": "ttwid=1%7Cb4VDDhMuiu210vBNpuwSX6y6mE3-Kd8V_KohiegkH6I%7C1752623822%7C5eca08c476400d14e217dbbd6e50aae83890316e48cece637820230753bb18fa; Domain=.tiktok.com; Path=/; Expires=1783727822; tt_csrf_token=Nv7oam43-bndgxVxY3eXfmxvAs6t4U4YPaEg; Domain=.tiktok.com; Path=/; Secure; tt_chain_token=\"GxvQVUpMnn24jeD3x5Z0SA==\"; Domain=.tiktok.com; Path=/; Secure; Expires=1768175823", "format": "bytevc1_540p_406241-1 - 576x1024", "_type": "video", "_version": {"version": "2025.06.30", "release_git_head": "b0187844988e557c7e1e6bb1aabd4c1176768d86", "repository": "yt-dlp/yt-dlp"}}