#!/usr/bin/env python3
"""
Social Media Tracker CLI - Easy command-line interface
Simple commands for managing automatic social media tracking
"""
import sys
import subprocess
from pathlib import Path


def run_tracker_command(args):
    """Run auto_tracker.py with given arguments"""
    script_path = Path(__file__).parent / "auto_tracker.py"
    cmd = [sys.executable, str(script_path)] + args
    
    try:
        result = subprocess.run(cmd, check=True)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        return False


def show_help():
    """Show help information"""
    help_text = """
🤖 Social Media Auto Tracker CLI

📋 QUICK COMMANDS:

🔄 DAEMON CONTROL:
  tracker start                    # Start automatic tracking daemon
  tracker start --interval 3600   # Start with 1-hour check interval
  tracker stop                     # Stop daemon (Ctrl+C)

👥 USER MANAGEMENT:
  tracker add instagram username   # Add Instagram user to tracking
  tracker add tiktok username      # Add TikTok user to tracking  
  tracker add vsco username        # Add VSCO user to tracking
  tracker remove instagram username # Remove user from tracking
  tracker list                     # List all tracked users
  tracker list instagram           # List Instagram users only

🔍 MANUAL OPERATIONS:
  tracker check                    # Check all users for new content
  tracker check instagram username # Check specific user
  tracker download                 # Download new content from all users
  tracker download instagram username # Download from specific user

📊 INFORMATION:
  tracker stats                    # Show tracking statistics
  tracker status                   # Show daemon status
  tracker logs                     # Show recent logs

⚙️ ADVANCED OPTIONS:
  tracker add instagram username --no-stories  # Skip stories
  tracker add instagram username --no-reels    # Skip reels
  tracker add tiktok username --no-posts       # Skip posts

📁 PATHS:
  Database: ~/.social_tracker/tracker.db
  Downloads: ~/Downloads/SocialMedia/
  Logs: ~/.social_tracker/tracker.log

💡 EXAMPLES:
  # Add users and start tracking
  tracker add instagram johndoe
  tracker add tiktok janedoe  
  tracker add vsco photographer
  tracker start

  # Check what's new without downloading
  tracker check

  # Manual download session
  tracker download

  # View statistics
  tracker stats
"""
    print(help_text)


def main():
    """Main CLI entry point"""
    if len(sys.argv) < 2:
        show_help()
        return
    
    command = sys.argv[1].lower()
    
    if command in ['help', '--help', '-h']:
        show_help()
        return
    
    elif command == 'start':
        # Start daemon
        args = ['daemon'] + sys.argv[2:]
        print("🚀 Starting automatic tracking daemon...")
        print("📱 Press Ctrl+C to stop")
        run_tracker_command(args)
    
    elif command == 'stop':
        print("⏹️ To stop the daemon, press Ctrl+C in the daemon terminal")
        print("💡 Or use: pkill -f auto_tracker.py")
    
    elif command == 'add':
        # Add user
        if len(sys.argv) < 4:
            print("❌ Usage: tracker add <platform> <username>")
            print("📱 Platforms: instagram, tiktok, vsco")
            return
        
        platform = sys.argv[2].lower()
        username = sys.argv[3]
        
        if platform not in ['instagram', 'tiktok', 'vsco']:
            print(f"❌ Unsupported platform: {platform}")
            print("📱 Supported: instagram, tiktok, vsco")
            return
        
        args = ['add', '--platform', platform, '--username', username] + sys.argv[4:]
        success = run_tracker_command(args)
        
        if success:
            print(f"✅ Added {platform} @{username} to tracking")
        else:
            print(f"❌ Failed to add {platform} @{username}")
    
    elif command == 'remove':
        # Remove user
        if len(sys.argv) < 4:
            print("❌ Usage: tracker remove <platform> <username>")
            return
        
        platform = sys.argv[2].lower()
        username = sys.argv[3]
        
        args = ['remove', '--platform', platform, '--username', username]
        success = run_tracker_command(args)
        
        if success:
            print(f"✅ Removed {platform} @{username} from tracking")
        else:
            print(f"❌ Failed to remove {platform} @{username}")
    
    elif command == 'list':
        # List users
        args = ['list']
        if len(sys.argv) > 2:
            platform = sys.argv[2].lower()
            if platform in ['instagram', 'tiktok', 'vsco']:
                args.extend(['--platform', platform])
        
        run_tracker_command(args)
    
    elif command == 'check':
        # Check for new content
        args = ['check']
        
        if len(sys.argv) > 2:
            if len(sys.argv) >= 4:
                # Specific user
                platform = sys.argv[2].lower()
                username = sys.argv[3]
                args.extend(['--platform', platform, '--username', username])
            else:
                print("❌ Usage: tracker check [platform username]")
                return
        
        print("🔍 Checking for new content...")
        run_tracker_command(args)
    
    elif command in ['download', 'dl']:
        # Download new content (same as check)
        args = ['check']
        
        if len(sys.argv) > 2:
            if len(sys.argv) >= 4:
                # Specific user
                platform = sys.argv[2].lower()
                username = sys.argv[3]
                args.extend(['--platform', platform, '--username', username])
            else:
                print("❌ Usage: tracker download [platform username]")
                return
        
        print("⬇️ Downloading new content...")
        run_tracker_command(args)
    
    elif command == 'stats':
        # Show statistics
        run_tracker_command(['stats'])
    
    elif command == 'status':
        # Show daemon status
        print("🔍 Checking daemon status...")
        try:
            result = subprocess.run(['pgrep', '-f', 'auto_tracker.py'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                pids = result.stdout.strip().split('\n')
                print(f"✅ Daemon running (PID: {', '.join(pids)})")
            else:
                print("⏹️ Daemon not running")
        except:
            print("❓ Unable to check daemon status")
    
    elif command == 'logs':
        # Show recent logs
        log_file = Path.home() / ".social_tracker" / "tracker.log"
        if log_file.exists():
            print(f"📋 Recent logs from {log_file}:")
            print("-" * 50)
            try:
                # Show last 20 lines
                result = subprocess.run(['tail', '-20', str(log_file)], 
                                      capture_output=True, text=True)
                print(result.stdout)
            except:
                # Fallback for systems without tail
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    for line in lines[-20:]:
                        print(line.rstrip())
        else:
            print("📋 No log file found")
    
    else:
        print(f"❌ Unknown command: {command}")
        print("💡 Use 'tracker help' for available commands")


if __name__ == '__main__':
    main()
