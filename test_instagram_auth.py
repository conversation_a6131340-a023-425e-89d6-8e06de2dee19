#!/usr/bin/env python3
"""
Test Instagram Authentication
Quick test script to verify Instagram authentication is working
"""
import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from instagram_downloader import InstagramDownloader
import config


def test_session_file():
    """Test Instagram authentication with session file"""
    print("🔐 Testing Instagram Session File Authentication")
    print("=" * 60)
    
    session_file = getattr(config, 'INSTAGRAM_SESSION_FILE', None)
    if not session_file:
        print("❌ No session file configured in config.py")
        print("   Set INSTAGRAM_SESSION_FILE = '/path/to/session/file'")
        return False
    
    if not os.path.exists(session_file):
        print(f"❌ Session file not found: {session_file}")
        print("   Run: python3 create_instagram_session.py")
        return False
    
    try:
        print(f"📁 Using session file: {session_file}")
        downloader = InstagramDownloader(session_file=session_file)
        
        if downloader.logged_in:
            print("✅ Session loaded successfully!")
            return True
        else:
            print("❌ Failed to load session")
            return False
            
    except Exception as e:
        print(f"❌ Error loading session: {e}")
        return False


def test_credentials():
    """Test Instagram authentication with username/password"""
    print("\n🔑 Testing Instagram Username/Password Authentication")
    print("=" * 60)
    
    username = getattr(config, 'INSTAGRAM_USERNAME', None)
    password = getattr(config, 'INSTAGRAM_PASSWORD', None)
    
    if not username or not password:
        print("❌ No credentials configured in config.py")
        print("   Set INSTAGRAM_USERNAME and INSTAGRAM_PASSWORD")
        return False
    
    try:
        print(f"👤 Logging in as: {username}")
        downloader = InstagramDownloader(username=username, password=password)
        
        if downloader.logged_in:
            print("✅ Login successful!")
            return True
        else:
            print("❌ Login failed")
            return False
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False


def test_public_access():
    """Test accessing public Instagram content without authentication"""
    print("\n🌐 Testing Public Instagram Access (No Auth)")
    print("=" * 60)
    
    try:
        downloader = InstagramDownloader()
        
        # Try to access a public profile
        print("🔍 Testing access to public profile...")
        
        # This will likely fail due to Instagram's restrictions
        # but we can test the downloader initialization
        print("✅ Downloader initialized (but may not work without auth)")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def main():
    """Main test function"""
    print("Instagram Authentication Test Suite")
    print("=" * 60)
    print("This script tests different Instagram authentication methods\n")
    
    results = []
    
    # Test 1: Session file (recommended)
    results.append(("Session File", test_session_file()))
    
    # Test 2: Username/password
    results.append(("Credentials", test_credentials()))
    
    # Test 3: Public access
    results.append(("Public Access", test_public_access()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == 0:
        print("\n🚨 ALL TESTS FAILED")
        print("Instagram authentication is not working!")
        print("\n💡 SOLUTIONS:")
        print("1. Run: python3 create_instagram_session.py")
        print("2. Configure session file in config.py")
        print("3. Check INSTAGRAM_SETUP.md for detailed instructions")
        return 1
    
    elif passed < len(results):
        print(f"\n⚠️  PARTIAL SUCCESS ({passed}/{len(results)})")
        print("Some authentication methods are working.")
        print("Session file method is recommended for best results.")
        return 0
    
    else:
        print("\n🎉 ALL TESTS PASSED!")
        print("Instagram authentication is working perfectly!")
        return 0


if __name__ == "__main__":
    sys.exit(main())
